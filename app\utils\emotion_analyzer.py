#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
情绪分析工具类
基于StructBERT模型进行中文情绪分类
"""

import os
import torch
import numpy as np
from transformers import BertTokenizer, BertForSequenceClassification
from flask import current_app
import logging

class EmotionAnalyzer:
    """情绪分析器"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.emotion_labels = {
            0: "恐惧",
            1: "愤怒", 
            2: "厌恶",
            3: "喜好",
            4: "悲伤",
            5: "高兴",
            6: "惊讶"
        }
        self.negative_emotions = {"恐惧", "愤怒", "厌恶", "悲伤"}
        self.positive_emotions = {"喜好", "高兴"}
        self.neutral_emotions = {"惊讶"}
        
    def load_model(self, model_path=None):
        """加载模型"""
        try:
            if model_path is None:
                model_path = current_app.config.get('MODEL_PATH')
            
            if not os.path.exists(model_path):
                raise FileNotFoundError(f"模型路径不存在: {model_path}")
            
            # 加载tokenizer
            self.tokenizer = BertTokenizer.from_pretrained(model_path)
            
            # 加载模型
            self.model = BertForSequenceClassification.from_pretrained(model_path)
            self.model.to(self.device)
            self.model.eval()
            
            logging.info(f"情绪分析模型加载成功，使用设备: {self.device}")
            return True
            
        except Exception as e:
            logging.error(f"模型加载失败: {str(e)}")
            return False
    
    def predict(self, text):
        """
        预测文本情绪
        
        Args:
            text (str): 待分析的文本
            
        Returns:
            dict: 包含预测结果的字典
        """
        if not self.model or not self.tokenizer:
            raise RuntimeError("模型未加载，请先调用load_model()方法")
        
        if not text or not text.strip():
            raise ValueError("输入文本不能为空")
        
        try:
            # 文本预处理
            text = text.strip()
            if len(text) > 500:  # 限制文本长度
                text = text[:500]
            
            # 编码文本
            inputs = self.tokenizer(
                text,
                return_tensors='pt',
                max_length=512,
                truncation=True,
                padding=True
            )
            
            # 移动到设备
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # 预测
            with torch.no_grad():
                outputs = self.model(**inputs)
                logits = outputs.logits
                probabilities = torch.softmax(logits, dim=-1)
            
            # 获取预测结果
            predicted_class = torch.argmax(probabilities, dim=-1).item()
            confidence = probabilities[0][predicted_class].item()
            
            # 获取所有情绪的概率
            all_probabilities = probabilities[0].cpu().numpy()
            emotion_scores = {}
            for i, prob in enumerate(all_probabilities):
                emotion_scores[self.emotion_labels[i]] = float(prob)
            
            # 判断情绪类型
            predicted_emotion = self.emotion_labels[predicted_class]
            is_negative = predicted_emotion in self.negative_emotions
            is_positive = predicted_emotion in self.positive_emotions
            
            result = {
                'text': text,
                'predicted_emotion': predicted_emotion,
                'confidence': float(confidence),
                'is_negative': is_negative,
                'is_positive': is_positive,
                'emotion_scores': emotion_scores,
                'predicted_class': predicted_class
            }
            
            return result
            
        except Exception as e:
            logging.error(f"情绪预测失败: {str(e)}")
            raise RuntimeError(f"情绪预测失败: {str(e)}")
    
    def batch_predict(self, texts):
        """
        批量预测文本情绪
        
        Args:
            texts (list): 待分析的文本列表
            
        Returns:
            list: 预测结果列表
        """
        if not texts:
            return []
        
        results = []
        for text in texts:
            try:
                result = self.predict(text)
                results.append(result)
            except Exception as e:
                logging.error(f"批量预测中单个文本失败: {text[:50]}... - {str(e)}")
                # 添加错误结果
                results.append({
                    'text': text,
                    'error': str(e),
                    'predicted_emotion': None,
                    'confidence': 0.0,
                    'is_negative': False,
                    'is_positive': False,
                    'emotion_scores': {},
                    'predicted_class': None
                })
        
        return results
    
    def analyze_emotion_trend(self, emotion_records):
        """
        分析情绪趋势
        
        Args:
            emotion_records (list): 情绪记录列表
            
        Returns:
            dict: 趋势分析结果
        """
        if not emotion_records:
            return {
                'total_records': 0,
                'emotion_distribution': {},
                'negative_ratio': 0.0,
                'positive_ratio': 0.0,
                'trend': 'stable'
            }
        
        # 统计情绪分布
        emotion_count = {}
        negative_count = 0
        positive_count = 0
        
        for record in emotion_records:
            emotion = record.get('predicted_emotion', '')
            emotion_count[emotion] = emotion_count.get(emotion, 0) + 1
            
            if emotion in self.negative_emotions:
                negative_count += 1
            elif emotion in self.positive_emotions:
                positive_count += 1
        
        total_records = len(emotion_records)
        negative_ratio = negative_count / total_records if total_records > 0 else 0
        positive_ratio = positive_count / total_records if total_records > 0 else 0
        
        # 简单趋势判断
        trend = 'stable'
        if negative_ratio > 0.6:
            trend = 'declining'
        elif positive_ratio > 0.6:
            trend = 'improving'
        
        return {
            'total_records': total_records,
            'emotion_distribution': emotion_count,
            'negative_ratio': negative_ratio,
            'positive_ratio': positive_ratio,
            'negative_count': negative_count,
            'positive_count': positive_count,
            'trend': trend
        }

# 全局情绪分析器实例
emotion_analyzer = EmotionAnalyzer()
