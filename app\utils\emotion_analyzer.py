"""
模拟情绪分析器 - 用于测试
"""

import logging
import random

class MockEmotionAnalyzer:
    """模拟情绪分析器"""
    
    def __init__(self):
        self.model = True  # 模拟模型已加载
        self.emotions = ['高兴', '悲伤', '愤怒', '恐惧', '厌恶', '喜好', '惊讶']
        self.negative_emotions = ['悲伤', '愤怒', '恐惧', '厌恶']
    
    def load_model(self):
        """模拟加载模型"""
        logging.info("模拟加载情绪分析模型")
        self.model = True
        return True
    
    def predict(self, text):
        """模拟预测"""
        if not text or not text.strip():
            raise ValueError("文本内容不能为空")
        
        # 简单的关键词匹配
        text_lower = text.lower()
        
        if any(word in text_lower for word in ['开心', '高兴', '快乐', '喜欢', '好']):
            predicted_emotion = '高兴'
        elif any(word in text_lower for word in ['难过', '悲伤', '伤心', '哭']):
            predicted_emotion = '悲伤'
        elif any(word in text_lower for word in ['生气', '愤怒', '气愤', '讨厌']):
            predicted_emotion = '愤怒'
        elif any(word in text_lower for word in ['害怕', '恐惧', '担心', '怕']):
            predicted_emotion = '恐惧'
        elif any(word in text_lower for word in ['厌恶', '恶心', '反感']):
            predicted_emotion = '厌恶'
        elif any(word in text_lower for word in ['喜好', '喜爱', '偏爱']):
            predicted_emotion = '喜好'
        elif any(word in text_lower for word in ['惊讶', '意外', '震惊']):
            predicted_emotion = '惊讶'
        else:
            # 随机选择一个情绪
            predicted_emotion = random.choice(self.emotions)
        
        # 生成模拟的置信度
        confidence = random.uniform(0.6, 0.95)
        
        # 生成情绪分数
        emotion_scores = {}
        remaining_prob = 1.0
        
        for emotion in self.emotions:
            if emotion == predicted_emotion:
                emotion_scores[emotion] = confidence
                remaining_prob -= confidence
            else:
                prob = random.uniform(0, remaining_prob / (len(self.emotions) - 1))
                emotion_scores[emotion] = prob
                remaining_prob -= prob
        
        # 归一化
        total = sum(emotion_scores.values())
        emotion_scores = {k: v/total for k, v in emotion_scores.items()}
        
        return {
            'predicted_emotion': predicted_emotion,
            'confidence': confidence,
            'is_negative': predicted_emotion in self.negative_emotions,
            'emotion_scores': emotion_scores
        }
    
    def batch_predict(self, texts):
        """批量预测"""
        results = []
        for i, text in enumerate(texts):
            try:
                result = self.predict(text)
                result['index'] = i
                results.append(result)
            except Exception as e:
                results.append({
                    'index': i,
                    'error': str(e)
                })
        return results

# 创建全局实例
emotion_analyzer = MockEmotionAnalyzer()
