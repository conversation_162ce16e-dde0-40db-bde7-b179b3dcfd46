#!/usr/bin/env python3
"""
简单模板测试 - 检查模板是否能正常渲染
"""

from flask import Flask, render_template
import os

app = Flask(__name__, template_folder='app/templates', static_folder='app/static')
app.config['SECRET_KEY'] = 'test-secret-key'

# 模拟用户对象
class MockUser:
    def __init__(self):
        self.is_authenticated = True
        self.real_name = "测试用户"
        self.username = "testuser"
        self.role = "student"
        self.student_id = "2023001"
        self.class_name = "高三(1)班"
        self.id = 1
    
    def is_student(self):
        return self.role == 'student'
    
    def is_teacher(self):
        return self.role == 'teacher'
    
    def is_admin(self):
        return self.role == 'admin'

# 模拟数据
mock_user = MockUser()
mock_emotion_records = []
mock_alerts = []

@app.context_processor
def inject_user():
    """注入用户上下文"""
    return dict(current_user=mock_user)

@app.route('/')
def index():
    return "<h1>模板测试服务器</h1><p>访问以下链接测试模板:</p>" + \
           "<ul>" + \
           "<li><a href='/test/dashboard'>仪表板</a></li>" + \
           "<li><a href='/test/analyze'>情绪分析</a></li>" + \
           "<li><a href='/test/history'>历史记录</a></li>" + \
           "<li><a href='/test/statistics'>统计分析</a></li>" + \
           "<li><a href='/test/profile'>个人资料</a></li>" + \
           "<li><a href='/test/admin'>管理员首页</a></li>" + \
           "<li><a href='/test/admin_users'>用户管理</a></li>" + \
           "<li><a href='/test/admin_alerts'>预警管理</a></li>" + \
           "<li><a href='/test/admin_reports'>系统报告</a></li>" + \
           "</ul>"

@app.route('/test/dashboard')
def test_dashboard():
    try:
        return render_template('dashboard.html')
    except Exception as e:
        return f"<h1>Dashboard模板错误</h1><pre>{str(e)}</pre>"

@app.route('/test/analyze')
def test_analyze():
    try:
        return render_template('analyze.html')
    except Exception as e:
        return f"<h1>Analyze模板错误</h1><pre>{str(e)}</pre>"

@app.route('/test/history')
def test_history():
    try:
        # 模拟分页对象
        class MockPagination:
            def __init__(self):
                self.items = []
                self.pages = 1
                self.page = 1
                self.has_prev = False
                self.has_next = False
                self.total = 0
                self.per_page = 10
        
        records = MockPagination()
        return render_template('history.html', records=records)
    except Exception as e:
        return f"<h1>History模板错误</h1><pre>{str(e)}</pre>"

@app.route('/test/statistics')
def test_statistics():
    try:
        return render_template('emotion/statistics.html')
    except Exception as e:
        return f"<h1>Statistics模板错误</h1><pre>{str(e)}</pre>"

@app.route('/test/profile')
def test_profile():
    try:
        return render_template('auth/profile.html')
    except Exception as e:
        return f"<h1>Profile模板错误</h1><pre>{str(e)}</pre>"

@app.route('/test/admin')
def test_admin():
    try:
        return render_template('admin/index.html')
    except Exception as e:
        return f"<h1>Admin Index模板错误</h1><pre>{str(e)}</pre>"

@app.route('/test/admin_users')
def test_admin_users():
    try:
        # 模拟用户分页对象
        class MockPagination:
            def __init__(self):
                self.items = [mock_user]
                self.pages = 1
                self.page = 1
                self.has_prev = False
                self.has_next = False
                self.total = 1
                self.per_page = 10
            
            def iter_pages(self):
                return [1]
        
        users = MockPagination()
        return render_template('admin/users.html', users=users)
    except Exception as e:
        return f"<h1>Admin Users模板错误</h1><pre>{str(e)}</pre>"

@app.route('/test/admin_alerts')
def test_admin_alerts():
    try:
        # 模拟预警分页对象
        class MockPagination:
            def __init__(self):
                self.items = []
                self.pages = 1
                self.page = 1
                self.has_prev = False
                self.has_next = False
                self.total = 0
                self.per_page = 10
            
            def iter_pages(self):
                return [1]
        
        alerts = MockPagination()
        return render_template('admin/alerts.html', alerts=alerts)
    except Exception as e:
        return f"<h1>Admin Alerts模板错误</h1><pre>{str(e)}</pre>"

@app.route('/test/admin_reports')
def test_admin_reports():
    try:
        # 模拟报告数据
        user_stats = {
            'students': 100,
            'teachers': 10,
            'admins': 2,
            'active_students': 80,
            'active_teachers': 8,
            'active_admins': 2,
            'student_records': 500,
            'teacher_records': 50,
            'admin_records': 10
        }
        
        alert_stats = {
            'high': 5,
            'medium': 15,
            'low': 30,
            'pending': 10,
            'processing': 20,
            'resolved': 20
        }
        
        return render_template('admin/reports.html',
                             total_users=112,
                             total_records=560,
                             total_alerts=50,
                             days=30,
                             user_stats=user_stats,
                             alert_stats=alert_stats)
    except Exception as e:
        return f"<h1>Admin Reports模板错误</h1><pre>{str(e)}</pre>"

if __name__ == '__main__':
    print("启动模板测试服务器...")
    print("访问 http://127.0.0.1:5000 查看测试页面")
    app.run(host='127.0.0.1', port=5000, debug=True)
