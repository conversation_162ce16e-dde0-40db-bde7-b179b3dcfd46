#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户模型
"""

from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from app import db

class User(UserMixin, db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    real_name = db.Column(db.String(50), nullable=False)
    
    # 用户类型：student(学生), teacher(教师), admin(管理员)
    user_type = db.Column(db.Enum('student', 'teacher', 'admin'), nullable=False, default='student')
    
    # 学生特有字段
    student_id = db.Column(db.String(20), unique=True, nullable=True, index=True)  # 学号
    class_name = db.Column(db.String(50), nullable=True)  # 班级
    grade = db.Column(db.String(20), nullable=True)  # 年级
    
    # 教师特有字段
    teacher_id = db.Column(db.String(20), unique=True, nullable=True, index=True)  # 教师工号
    subject = db.Column(db.String(50), nullable=True)  # 任教科目
    
    # 通用字段
    phone = db.Column(db.String(20), nullable=True)
    avatar = db.Column(db.String(200), nullable=True)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # 关系
    emotion_records = db.relationship('EmotionRecord', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def is_student(self):
        """判断是否为学生"""
        return self.user_type == 'student'
    
    def is_teacher(self):
        """判断是否为教师"""
        return self.user_type == 'teacher'
    
    def is_admin(self):
        """判断是否为管理员"""
        return self.user_type == 'admin'
    
    def get_display_name(self):
        """获取显示名称"""
        if self.user_type == 'student' and self.student_id:
            return f"{self.real_name}({self.student_id})"
        elif self.user_type == 'teacher' and self.teacher_id:
            return f"{self.real_name}老师({self.teacher_id})"
        return self.real_name
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'real_name': self.real_name,
            'user_type': self.user_type,
            'student_id': self.student_id,
            'class_name': self.class_name,
            'grade': self.grade,
            'teacher_id': self.teacher_id,
            'subject': self.subject,
            'phone': self.phone,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'display_name': self.get_display_name()
        }
    
    def __repr__(self):
        return f'<User {self.username}>'
