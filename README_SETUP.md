# 黔南民族中学学生情绪分析系统 - 安装指南

## 🎯 项目概述

这是一个基于Flask和StructBERT的学生情绪分析系统，支持多角色用户管理、情绪分析、数据可视化和预警功能。

## 📋 功能特性

### ✅ 已完成功能
- **用户管理**: 学生、教师、管理员三种角色
- **情绪分析**: 基于StructBERT的中文情绪识别
- **数据可视化**: Chart.js图表展示情绪统计
- **历史记录**: 完整的记录查看、筛选、分页功能
- **统计分析**: 多维度情绪数据分析和趋势展示
- **预警系统**: 负面情绪自动检测和分级预警
- **管理员界面**: 用户管理、预警管理、系统报告
- **个人资料**: 用户资料查看和编辑功能

## 🚀 快速开始

### 方法一：使用测试版本（推荐）

如果遇到依赖安装问题，可以先使用测试版本查看所有功能：

```bash
# 启动测试服务器
python test_app.py

# 或使用模板测试服务器
python simple_template_test.py
```

访问 http://127.0.0.1:5000 查看所有页面效果。

### 方法二：完整安装

#### 1. 环境要求
- Python 3.8+
- pip 或 conda

#### 2. 安装依赖

**使用pip安装：**
```bash
pip install flask flask-sqlalchemy flask-login flask-wtf pymysql python-dotenv werkzeug
```

**使用conda安装：**
```bash
conda install -c conda-forge flask flask-sqlalchemy flask-login flask-wtf pymysql python-dotenv werkzeug
```

**可选依赖（AI功能）：**
```bash
pip install transformers torch numpy pandas scikit-learn
```

#### 3. 数据库配置

**使用SQLite（简单）：**
系统已配置SQLite作为默认数据库，无需额外配置。

**使用MySQL（生产环境）：**
1. 安装MySQL服务器
2. 创建数据库：
```sql
CREATE DATABASE emotion_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```
3. 修改 `config.py` 中的数据库连接字符串

#### 4. 启动应用

```bash
# 启动完整应用
python app.py

# 或使用Flask命令
flask run
```

访问 http://127.0.0.1:5000

## 📁 项目结构

```
emo/
├── app/                    # 应用主目录
│   ├── __init__.py        # 应用工厂
│   ├── models/            # 数据模型
│   │   ├── user.py       # 用户模型
│   │   └── emotion.py    # 情绪记录模型
│   ├── views/             # 视图蓝图
│   │   ├── main.py       # 主要路由
│   │   ├── auth.py       # 认证路由
│   │   ├── admin.py      # 管理员路由
│   │   └── emotion.py    # 情绪分析路由
│   ├── templates/         # HTML模板
│   │   ├── base.html     # 基础模板
│   │   ├── admin/        # 管理员页面
│   │   ├── auth/         # 认证页面
│   │   └── emotion/      # 情绪分析页面
│   ├── static/            # 静态文件
│   │   ├── css/          # 样式文件
│   │   ├── js/           # JavaScript文件
│   │   └── images/       # 图片文件
│   └── utils/             # 工具模块
│       └── emotion_analyzer.py  # 情绪分析器
├── config.py              # 配置文件
├── app.py                 # 应用入口
├── test_app.py           # 测试版本
├── simple_template_test.py # 模板测试
└── requirements.txt       # 依赖列表
```

## 🔧 故障排除

### 1. 依赖安装失败

**问题**: `ModuleNotFoundError: No module named 'flask_sqlalchemy'`

**解决方案**:
```bash
# 方法1: 使用测试版本
python test_app.py

# 方法2: 手动安装依赖
python -m pip install --upgrade pip
python -m pip install flask flask-sqlalchemy flask-login flask-wtf

# 方法3: 使用conda
conda install -c conda-forge flask flask-sqlalchemy flask-login flask-wtf
```

### 2. 路由重复错误

**问题**: `AssertionError: View function mapping is overwriting an existing endpoint function`

**解决方案**: 已修复，确保使用最新版本的代码。

### 3. 数据库连接问题

**问题**: 数据库连接失败

**解决方案**:
- 使用SQLite（默认配置）
- 检查MySQL服务是否启动
- 验证数据库连接字符串

## 🎨 页面预览

### 主要页面
- **首页**: 系统介绍和快速导航
- **仪表板**: 个人情绪数据概览
- **情绪分析**: 文本情绪分析功能
- **历史记录**: 情绪记录查看和管理
- **统计分析**: 数据可视化和趋势分析

### 管理员页面
- **用户管理**: 用户增删改查
- **预警管理**: 情绪预警处理
- **系统报告**: 数据统计和导出

### 个人中心
- **个人资料**: 用户信息查看和编辑
- **密码修改**: 安全设置

## 🔐 默认账户

系统启动后会自动创建管理员账户：
- 用户名: admin
- 密码: admin123
- 角色: 管理员

## 📞 技术支持

如果遇到问题，请检查：
1. Python版本是否为3.8+
2. 依赖是否正确安装
3. 数据库连接是否正常
4. 端口5000是否被占用

## 🎉 项目完成状态

✅ 所有核心功能已完成
✅ 所有模板页面已创建
✅ API接口功能完整
✅ 数据库模型完善
✅ 用户权限控制
✅ 错误处理机制
✅ 响应式设计

项目已可投入使用！🚀
