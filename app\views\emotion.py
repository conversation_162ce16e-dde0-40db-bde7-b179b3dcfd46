#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
情绪分析相关视图
"""

from flask import Blueprint, render_template, request, jsonify, flash
from flask_login import login_required, current_user
from app import db
from app.models.emotion import EmotionRecord, EmotionDetail, EmotionAlert
from app.utils.emotion_analyzer import emotion_analyzer
from datetime import datetime, timedelta
import logging

bp = Blueprint('emotion', __name__)

@bp.route('/statistics')
@login_required
def statistics():
    """情绪统计页面"""
    try:
        # 获取时间范围参数
        days = request.args.get('days', 30, type=int)
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # 查询用户的情绪记录
        if current_user.is_student():
            records = EmotionRecord.query.filter(
                EmotionRecord.user_id == current_user.id,
                EmotionRecord.created_at >= start_date
            ).all()
        elif current_user.is_teacher() or current_user.is_admin():
            # 教师和管理员可以查看所有学生的统计
            records = EmotionRecord.query.filter(
                EmotionRecord.created_at >= start_date
            ).all()
        else:
            records = []
        
        # 使用情绪分析器进行趋势分析
        analysis_result = emotion_analyzer.analyze_emotion_trend([
            {
                'predicted_emotion': record.predicted_emotion,
                'confidence': record.confidence_score,
                'created_at': record.created_at
            } for record in records
        ])
        
        return render_template('emotion/statistics.html',
                             analysis_result=analysis_result,
                             records=records,
                             days=days)
        
    except Exception as e:
        logging.error(f"统计页面加载失败: {str(e)}")
        flash('统计数据加载失败，请稍后重试', 'error')
        return render_template('emotion/statistics.html',
                             analysis_result={},
                             records=[],
                             days=30)

@bp.route('/alerts')
@login_required
def alerts():
    """情绪预警页面"""
    try:
        if current_user.is_student():
            # 学生只能看自己的预警
            alerts = EmotionAlert.query.filter_by(user_id=current_user.id)\
                .order_by(EmotionAlert.created_at.desc()).all()
        elif current_user.is_teacher() or current_user.is_admin():
            # 教师和管理员可以看所有预警
            alerts = EmotionAlert.query.order_by(EmotionAlert.created_at.desc()).all()
        else:
            alerts = []
        
        return render_template('emotion/alerts.html', alerts=alerts)
        
    except Exception as e:
        logging.error(f"预警页面加载失败: {str(e)}")
        flash('预警数据加载失败，请稍后重试', 'error')
        return render_template('emotion/alerts.html', alerts=[])

@bp.route('/api/statistics')
@login_required
def api_statistics():
    """情绪统计API"""
    try:
        days = request.args.get('days', 30, type=int)
        start_date = datetime.utcnow() - timedelta(days=days)
        
        if current_user.is_student():
            records = EmotionRecord.query.filter(
                EmotionRecord.user_id == current_user.id,
                EmotionRecord.created_at >= start_date
            ).all()
        else:
            records = EmotionRecord.query.filter(
                EmotionRecord.created_at >= start_date
            ).all()
        
        # 按日期分组统计
        daily_stats = {}
        emotion_distribution = {}
        
        for record in records:
            date_key = record.created_at.strftime('%Y-%m-%d')
            emotion = record.predicted_emotion
            
            if date_key not in daily_stats:
                daily_stats[date_key] = {
                    'total': 0,
                    'negative': 0,
                    'positive': 0,
                    'emotions': {}
                }
            
            daily_stats[date_key]['total'] += 1
            daily_stats[date_key]['emotions'][emotion] = daily_stats[date_key]['emotions'].get(emotion, 0) + 1
            
            if record.is_negative:
                daily_stats[date_key]['negative'] += 1
            else:
                daily_stats[date_key]['positive'] += 1
            
            emotion_distribution[emotion] = emotion_distribution.get(emotion, 0) + 1
        
        return jsonify({
            'daily_stats': daily_stats,
            'emotion_distribution': emotion_distribution,
            'total_records': len(records)
        })
        
    except Exception as e:
        logging.error(f"统计API失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@bp.route('/api/batch_analyze', methods=['POST'])
@login_required
def api_batch_analyze():
    """批量情绪分析API"""
    try:
        data = request.get_json()
        if not data or 'texts' not in data:
            return jsonify({'error': '缺少texts参数'}), 400
        
        texts = data['texts']
        if not isinstance(texts, list) or not texts:
            return jsonify({'error': 'texts必须是非空数组'}), 400
        
        # 确保模型已加载
        if not emotion_analyzer.model:
            if not emotion_analyzer.load_model():
                return jsonify({'error': '模型加载失败'}), 500
        
        # 批量分析
        results = emotion_analyzer.batch_predict(texts)
        
        # 可选：保存到数据库
        if data.get('save', False):
            saved_records = []
            for result in results:
                if 'error' not in result:
                    emotion_record = EmotionRecord(
                        user_id=current_user.id,
                        text_content=result['text'],
                        source_type=data.get('source_type', 'batch_api'),
                        predicted_emotion=result['predicted_emotion'],
                        confidence_score=result['confidence'],
                        is_negative=result['is_negative']
                    )
                    db.session.add(emotion_record)
                    saved_records.append(emotion_record)
            
            db.session.commit()
            
            # 添加记录ID到结果中
            for i, record in enumerate(saved_records):
                if i < len(results) and 'error' not in results[i]:
                    results[i]['record_id'] = record.id
        
        return jsonify({
            'results': results,
            'total': len(results),
            'success_count': len([r for r in results if 'error' not in r]),
            'error_count': len([r for r in results if 'error' in r])
        })
        
    except Exception as e:
        logging.error(f"批量分析API失败: {str(e)}")
        return jsonify({'error': str(e)}), 500
