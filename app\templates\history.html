{% extends "base.html" %}

{% block title %}历史记录 - 黔南民族中学学生情绪分析系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-history text-primary"></i> 
                {% if current_user.is_student() %}
                    我的情绪记录
                {% else %}
                    学生情绪记录
                {% endif %}
            </h2>
            <div>
                <a href="{{ url_for('main.analyze') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> 新增分析
                </a>
            </div>
        </div>

        <!-- 筛选器 -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="emotion_filter" class="form-label">情绪类型</label>
                        <select class="form-select" id="emotion_filter" name="emotion">
                            <option value="">全部情绪</option>
                            <option value="高兴" {% if request.args.get('emotion') == '高兴' %}selected{% endif %}>高兴</option>
                            <option value="喜好" {% if request.args.get('emotion') == '喜好' %}selected{% endif %}>喜好</option>
                            <option value="惊讶" {% if request.args.get('emotion') == '惊讶' %}selected{% endif %}>惊讶</option>
                            <option value="悲伤" {% if request.args.get('emotion') == '悲伤' %}selected{% endif %}>悲伤</option>
                            <option value="愤怒" {% if request.args.get('emotion') == '愤怒' %}selected{% endif %}>愤怒</option>
                            <option value="恐惧" {% if request.args.get('emotion') == '恐惧' %}selected{% endif %}>恐惧</option>
                            <option value="厌恶" {% if request.args.get('emotion') == '厌恶' %}selected{% endif %}>厌恶</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="source_filter" class="form-label">数据来源</label>
                        <select class="form-select" id="source_filter" name="source">
                            <option value="">全部来源</option>
                            <option value="manual" {% if request.args.get('source') == 'manual' %}selected{% endif %}>手动输入</option>
                            <option value="diary" {% if request.args.get('source') == 'diary' %}selected{% endif %}>日记记录</option>
                            <option value="chat" {% if request.args.get('source') == 'chat' %}selected{% endif %}>聊天对话</option>
                            <option value="survey" {% if request.args.get('source') == 'survey' %}selected{% endif %}>问卷调查</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="date_filter" class="form-label">时间范围</label>
                        <select class="form-select" id="date_filter" name="days">
                            <option value="7" {% if request.args.get('days') == '7' %}selected{% endif %}>最近7天</option>
                            <option value="30" {% if request.args.get('days') == '30' or not request.args.get('days') %}selected{% endif %}>最近30天</option>
                            <option value="90" {% if request.args.get('days') == '90' %}selected{% endif %}>最近90天</option>
                            <option value="365" {% if request.args.get('days') == '365' %}selected{% endif %}>最近一年</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-filter"></i> 筛选
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 记录列表 -->
        {% if records and records.items %}
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>时间</th>
                                {% if not current_user.is_student() %}
                                <th>学生</th>
                                {% endif %}
                                <th>文本内容</th>
                                <th>预测情绪</th>
                                <th>置信度</th>
                                <th>情绪类型</th>
                                <th>数据来源</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in records.items %}
                            <tr>
                                <td>
                                    <small class="text-muted">
                                        {{ record.created_at.strftime('%Y-%m-%d') }}<br>
                                        {{ record.created_at.strftime('%H:%M:%S') }}
                                    </small>
                                </td>
                                {% if not current_user.is_student() %}
                                <td>
                                    <span class="badge bg-info">
                                        {{ record.user.real_name if record.user else '未知用户' }}
                                    </span>
                                </td>
                                {% endif %}
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" 
                                         data-bs-toggle="tooltip" 
                                         title="{{ record.text_content }}">
                                        {{ record.text_content }}
                                    </div>
                                </td>
                                <td>
                                    <span class="emotion-label emotion-{{ record.predicted_emotion }}">
                                        {{ record.predicted_emotion }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2">{{ "%.1f"|format(record.confidence_score * 100) }}%</span>
                                        <div class="progress" style="width: 60px; height: 6px;">
                                            <div class="progress-bar" 
                                                 style="width: {{ record.confidence_score * 100 }}%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if record.is_negative %}
                                        <span class="badge bg-danger">负面</span>
                                    {% elif record.predicted_emotion in ['高兴', '喜好'] %}
                                        <span class="badge bg-success">正面</span>
                                    {% else %}
                                        <span class="badge bg-secondary">中性</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark">{{ record.source_type }}</span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-info" 
                                            onclick="showRecordDetail({{ record.id }})">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    {% if current_user.is_admin() or (current_user.is_student() and record.user_id == current_user.id) %}
                                    <button class="btn btn-sm btn-outline-danger" 
                                            onclick="deleteRecord({{ record.id }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                {% if records.pages > 1 %}
                <nav aria-label="记录分页">
                    <ul class="pagination justify-content-center mt-4">
                        {% if records.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main.history', page=records.prev_num, **request.args) }}">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        {% endif %}

                        {% for page_num in records.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != records.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('main.history', page=page_num, **request.args) }}">
                                        {{ page_num }}
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if records.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main.history', page=records.next_num, **request.args) }}">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                <!-- 统计信息 -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <small class="text-muted">
                            显示第 {{ records.per_page * (records.page - 1) + 1 }} - 
                            {{ records.per_page * (records.page - 1) + records.items|length }} 条，
                            共 {{ records.total }} 条记录
                        </small>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <!-- 空状态 -->
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-inbox fa-4x text-muted mb-4"></i>
                <h4 class="text-muted">暂无记录</h4>
                <p class="text-muted mb-4">
                    {% if current_user.is_student() %}
                        您还没有进行过情绪分析，开始记录您的心情吧！
                    {% else %}
                        当前筛选条件下没有找到相关记录。
                    {% endif %}
                </p>
                {% if current_user.is_student() %}
                <a href="{{ url_for('main.analyze') }}" class="btn btn-primary">
                    <i class="fas fa-brain"></i> 开始分析
                </a>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- 记录详情模态框 -->
<div class="modal fade" id="recordDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">记录详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="recordDetailContent">
                <!-- 详情内容将通过AJAX加载 -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function showRecordDetail(recordId) {
    // 显示加载状态
    $('#recordDetailContent').html('<div class="text-center"><div class="spinner-border" role="status"></div></div>');
    $('#recordDetailModal').modal('show');
    
    // 加载记录详情
    $.get(`/api/emotion/record/${recordId}`)
        .done(function(data) {
            var html = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr><td>时间:</td><td>${new Date(data.created_at).toLocaleString()}</td></tr>
                            <tr><td>情绪:</td><td><span class="emotion-label emotion-${data.predicted_emotion}">${data.predicted_emotion}</span></td></tr>
                            <tr><td>置信度:</td><td>${(data.confidence_score * 100).toFixed(1)}%</td></tr>
                            <tr><td>来源:</td><td>${data.source_type}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>详细分数</h6>
                        <div id="emotionScores"></div>
                    </div>
                </div>
                <div class="mt-3">
                    <h6>文本内容</h6>
                    <div class="border rounded p-3 bg-light">
                        ${data.text_content}
                    </div>
                </div>
            `;
            $('#recordDetailContent').html(html);
            
            // 加载详细分数
            if (data.emotion_details && data.emotion_details.length > 0) {
                var scoresHtml = '';
                data.emotion_details.forEach(function(detail) {
                    var percentage = (detail.probability * 100).toFixed(1);
                    scoresHtml += `
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>${detail.emotion_type}</span>
                            <div class="d-flex align-items-center">
                                <span class="me-2">${percentage}%</span>
                                <div class="progress" style="width: 100px; height: 6px;">
                                    <div class="progress-bar" style="width: ${percentage}%"></div>
                                </div>
                            </div>
                        </div>
                    `;
                });
                $('#emotionScores').html(scoresHtml);
            }
        })
        .fail(function() {
            $('#recordDetailContent').html('<div class="alert alert-danger">加载失败，请稍后重试</div>');
        });
}

function deleteRecord(recordId) {
    if (!confirm('确定要删除这条记录吗？')) {
        return;
    }
    
    $.ajax({
        url: `/api/emotion/record/${recordId}`,
        method: 'DELETE',
        success: function() {
            location.reload();
        },
        error: function() {
            alert('删除失败，请稍后重试');
        }
    });
}
</script>
{% endblock %}
