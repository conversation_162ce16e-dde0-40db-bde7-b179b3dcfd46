# 黔南民族中学学生情绪分析系统 - 部署指南

## 系统要求

### 硬件要求
- **CPU**: 4核心或以上
- **内存**: 8GB RAM (最低4GB)
- **存储**: 20GB可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Windows 10/11, Ubuntu 18.04+, CentOS 7+
- **Python**: 3.8 或更高版本
- **MySQL**: 8.0 或更高版本
- **Web服务器**: Nginx (生产环境推荐)

## 开发环境部署

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd emotion-analysis-system

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境 (Windows)
venv\Scripts\activate

# 激活虚拟环境 (Linux/Mac)
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 数据库配置

```bash
# 启动MySQL服务
# Windows: 在服务管理器中启动MySQL
# Linux: sudo systemctl start mysql

# 创建数据库
mysql -u root -p < database_init.sql

# 或手动创建
mysql -u root -p
CREATE DATABASE emotion_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. 环境配置

```bash
# 复制配置文件
cp .env.example .env

# 编辑 .env 文件，设置以下参数：
# MYSQL_HOST=localhost
# MYSQL_PORT=3306
# MYSQL_USER=root
# MYSQL_PASSWORD=your_password
# MYSQL_DATABASE=emotion_analysis
# SECRET_KEY=your-secret-key-here
```

### 4. 启动开发服务器

```bash
# 使用启动脚本（推荐）
python run.py

# 或直接启动
python app.py
```

访问 http://localhost:5000

## 生产环境部署

### 1. 服务器准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要软件
sudo apt install python3 python3-pip python3-venv mysql-server nginx git -y

# 配置MySQL
sudo mysql_secure_installation
```

### 2. 应用部署

```bash
# 创建应用目录
sudo mkdir -p /var/www/emotion-analysis
cd /var/www/emotion-analysis

# 克隆代码
sudo git clone <repository-url> .

# 设置权限
sudo chown -R www-data:www-data /var/www/emotion-analysis

# 创建虚拟环境
sudo -u www-data python3 -m venv venv
sudo -u www-data venv/bin/pip install -r requirements.txt
```

### 3. 配置Gunicorn

创建 `/etc/systemd/system/emotion-analysis.service`:

```ini
[Unit]
Description=Emotion Analysis System
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/var/www/emotion-analysis
Environment="PATH=/var/www/emotion-analysis/venv/bin"
ExecStart=/var/www/emotion-analysis/venv/bin/gunicorn --workers 4 --bind unix:emotion-analysis.sock -m 007 app:app
Restart=always

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
sudo systemctl daemon-reload
sudo systemctl start emotion-analysis
sudo systemctl enable emotion-analysis
```

### 4. 配置Nginx

创建 `/etc/nginx/sites-available/emotion-analysis`:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        include proxy_params;
        proxy_pass http://unix:/var/www/emotion-analysis/emotion-analysis.sock;
    }

    location /static {
        alias /var/www/emotion-analysis/app/static;
        expires 30d;
    }

    client_max_body_size 16M;
}
```

启用站点：

```bash
sudo ln -s /etc/nginx/sites-available/emotion-analysis /etc/nginx/sites-enabled
sudo nginx -t
sudo systemctl restart nginx
```

### 5. SSL配置 (可选)

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取SSL证书
sudo certbot --nginx -d your-domain.com
```

## Docker部署 (推荐)

### 1. 创建Dockerfile

```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 5000

CMD ["gunicorn", "--bind", "0.0.0.0:5000", "app:app"]
```

### 2. 创建docker-compose.yml

```yaml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "5000:5000"
    environment:
      - MYSQL_HOST=db
      - MYSQL_USER=root
      - MYSQL_PASSWORD=password
      - MYSQL_DATABASE=emotion_analysis
    depends_on:
      - db
    volumes:
      - ./uploads:/app/uploads

  db:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=emotion_analysis
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"

volumes:
  mysql_data:
```

### 3. 启动容器

```bash
docker-compose up -d
```

## 性能优化

### 1. 数据库优化

```sql
-- 添加索引
CREATE INDEX idx_emotion_records_user_created ON emotion_records(user_id, created_at);
CREATE INDEX idx_emotion_alerts_status ON emotion_alerts(status);

-- 配置MySQL
# 在 /etc/mysql/mysql.conf.d/mysqld.cnf 中添加：
innodb_buffer_pool_size = 1G
query_cache_size = 256M
```

### 2. 应用优化

```python
# 在 config.py 中添加缓存配置
CACHE_TYPE = "simple"
CACHE_DEFAULT_TIMEOUT = 300

# 启用压缩
COMPRESS_MIMETYPES = ['text/html', 'text/css', 'application/json']
```

### 3. 静态文件优化

```bash
# 使用CDN或配置静态文件缓存
# 在Nginx配置中添加：
location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## 监控与维护

### 1. 日志配置

```python
# 在应用中添加日志配置
import logging
from logging.handlers import RotatingFileHandler

if not app.debug:
    file_handler = RotatingFileHandler('logs/emotion-analysis.log', maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
```

### 2. 健康检查

```python
# 添加健康检查端点
@app.route('/health')
def health_check():
    return {'status': 'healthy', 'timestamp': datetime.utcnow().isoformat()}
```

### 3. 备份策略

```bash
# 数据库备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u root -p emotion_analysis > backup_$DATE.sql
```

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型文件是否完整
   - 确认Python版本兼容性
   - 检查内存是否充足

2. **数据库连接失败**
   - 验证数据库服务是否运行
   - 检查连接参数配置
   - 确认防火墙设置

3. **静态文件404**
   - 检查Nginx配置
   - 确认文件路径正确
   - 验证文件权限

### 性能问题

1. **响应缓慢**
   - 检查数据库查询性能
   - 监控服务器资源使用
   - 考虑添加缓存

2. **内存占用过高**
   - 调整Gunicorn worker数量
   - 优化模型加载策略
   - 检查内存泄漏

## 安全建议

1. **定期更新依赖包**
2. **使用强密码和密钥**
3. **启用HTTPS**
4. **配置防火墙**
5. **定期备份数据**
6. **监控异常访问**

## 联系支持

如遇到部署问题，请联系：
- 技术支持邮箱: [<EMAIL>]
- 项目文档: [documentation-url]
- 问题反馈: [issues-url]
