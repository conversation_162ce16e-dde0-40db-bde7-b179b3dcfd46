{% extends "base.html" %}

{% block title %}个人资料 - 黔南民族中学学生情绪分析系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-4">
        <!-- 用户信息卡片 -->
        <div class="card">
            <div class="card-body text-center">
                <div class="avatar-large mb-3">
                    {{ current_user.real_name[0] if current_user.real_name else current_user.username[0] }}
                </div>
                <h4>{{ current_user.real_name or current_user.username }}</h4>
                <p class="text-muted">{{ current_user.username }}</p>
                
                {% if current_user.is_admin() %}
                    <span class="badge bg-danger mb-3">管理员</span>
                {% elif current_user.is_teacher() %}
                    <span class="badge bg-info mb-3">教师</span>
                {% else %}
                    <span class="badge bg-success mb-3">学生</span>
                {% endif %}
                
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="text-primary">{{ emotion_stats.total_records }}</h5>
                            <small class="text-muted">情绪记录</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="text-success">{{ emotion_stats.positive_count }}</h5>
                            <small class="text-muted">正面情绪</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h5 class="text-danger">{{ emotion_stats.negative_count }}</h5>
                        <small class="text-muted">负面情绪</small>
                    </div>
                </div>
                
                <div class="d-grid gap-2 mt-4">
                    <a href="{{ url_for('auth.edit_profile') }}" class="btn btn-primary">
                        <i class="fas fa-edit"></i> 编辑资料
                    </a>
                    <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-key"></i> 修改密码
                    </a>
                </div>
            </div>
        </div>

        <!-- 账户信息 -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle text-primary"></i> 账户信息
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-sm table-borderless">
                    <tr>
                        <td class="text-muted">注册时间:</td>
                        <td>{{ current_user.created_at.strftime('%Y-%m-%d') }}</td>
                    </tr>
                    <tr>
                        <td class="text-muted">最后登录:</td>
                        <td>
                            {% if current_user.last_login %}
                                {{ current_user.last_login.strftime('%Y-%m-%d %H:%M') }}
                            {% else %}
                                从未登录
                            {% endif %}
                        </td>
                    </tr>
                    {% if current_user.is_student() %}
                        {% if current_user.student_id %}
                        <tr>
                            <td class="text-muted">学号:</td>
                            <td>{{ current_user.student_id }}</td>
                        </tr>
                        {% endif %}
                        {% if current_user.class_name %}
                        <tr>
                            <td class="text-muted">班级:</td>
                            <td>{{ current_user.class_name }}</td>
                        </tr>
                        {% endif %}
                    {% endif %}
                    <tr>
                        <td class="text-muted">邮箱:</td>
                        <td>{{ current_user.email or '未设置' }}</td>
                    </tr>
                    <tr>
                        <td class="text-muted">状态:</td>
                        <td>
                            {% if current_user.is_active %}
                                <span class="badge bg-success">正常</span>
                            {% else %}
                                <span class="badge bg-danger">禁用</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="col-lg-8">
        <!-- 情绪统计图表 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie text-primary"></i> 我的情绪分布
                </h6>
            </div>
            <div class="card-body">
                {% if emotion_stats.emotion_distribution %}
                <div class="chart-container" style="height: 300px;">
                    <canvas id="emotionChart"></canvas>
                </div>
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-chart-pie fa-3x mb-3"></i>
                    <h5>暂无数据</h5>
                    <p>您还没有进行过情绪分析</p>
                    <a href="{{ url_for('main.analyze') }}" class="btn btn-primary">
                        <i class="fas fa-brain"></i> 开始分析
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- 最近情绪记录 -->
        <div class="card mt-3">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-history text-primary"></i> 最近记录
                    </h6>
                    <a href="{{ url_for('main.history') }}" class="btn btn-sm btn-outline-primary">
                        查看全部
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if recent_records %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>内容</th>
                                <th>情绪</th>
                                <th>置信度</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in recent_records %}
                            <tr>
                                <td>
                                    <small>{{ record.created_at.strftime('%m-%d %H:%M') }}</small>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" 
                                         data-bs-toggle="tooltip" 
                                         title="{{ record.text_content }}">
                                        {{ record.text_content }}
                                    </div>
                                </td>
                                <td>
                                    <span class="emotion-label emotion-{{ record.predicted_emotion }}">
                                        {{ record.predicted_emotion }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2">{{ "%.1f"|format(record.confidence_score * 100) }}%</span>
                                        <div class="progress" style="width: 60px; height: 6px;">
                                            <div class="progress-bar" 
                                                 style="width: {{ record.confidence_score * 100 }}%"></div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <h5>暂无记录</h5>
                    <p>您还没有进行过情绪分析</p>
                    <a href="{{ url_for('main.analyze') }}" class="btn btn-primary">
                        <i class="fas fa-brain"></i> 开始分析
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- 情绪健康评估 -->
        {% if emotion_stats.total_records > 0 %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-heart text-primary"></i> 情绪健康评估
                </h6>
            </div>
            <div class="card-body">
                {% set positive_ratio = emotion_stats.positive_count / emotion_stats.total_records %}
                <div class="row">
                    <div class="col-md-6">
                        <h6>整体评估</h6>
                        {% if positive_ratio >= 0.7 %}
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                <strong>情绪状态良好</strong><br>
                                您的正面情绪占比较高，心理状态健康。
                            </div>
                        {% elif positive_ratio >= 0.4 %}
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>情绪状态一般</strong><br>
                                建议关注情绪变化，适当调节心情。
                            </div>
                        {% else %}
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle"></i>
                                <strong>需要关注</strong><br>
                                负面情绪较多，建议寻求专业帮助。
                            </div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <h6>情绪比例</h6>
                        <div class="progress mb-2" style="height: 20px;">
                            <div class="progress-bar bg-success" 
                                 style="width: {{ positive_ratio * 100 }}%">
                                正面 {{ "%.1f"|format(positive_ratio * 100) }}%
                            </div>
                            <div class="progress-bar bg-danger" 
                                 style="width: {{ (1 - positive_ratio) * 100 }}%">
                                负面 {{ "%.1f"|format((1 - positive_ratio) * 100) }}%
                            </div>
                        </div>
                        <small class="text-muted">
                            基于最近 {{ emotion_stats.total_records }} 条记录的分析
                        </small>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 情绪分布图表
    {% if emotion_stats.emotion_distribution %}
    var ctx = document.getElementById('emotionChart').getContext('2d');
    var distributionData = {{ emotion_stats.emotion_distribution|tojson }};
    
    var labels = Object.keys(distributionData);
    var data = Object.values(distributionData);
    var colors = labels.map(emotion => window.chartColors.emotions[emotion] || '#6c757d');
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors,
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            var total = context.dataset.data.reduce((a, b) => a + b, 0);
                            var percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                        }
                    }
                }
            }
        }
    });
    {% endif %}
});
</script>

<style>
.avatar-large {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    text-transform: uppercase;
    margin: 0 auto;
}

.chart-container {
    position: relative;
}
</style>
{% endblock %}
