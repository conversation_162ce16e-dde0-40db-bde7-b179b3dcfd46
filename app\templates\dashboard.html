{% extends "base.html" %}

{% block title %}仪表板 - 黔南民族中学学生情绪分析系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-tachometer-alt text-primary"></i> 
            欢迎回来，{{ current_user.real_name }}！
        </h2>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card dashboard-card bg-primary">
            <div class="card-body text-center">
                <i class="fas fa-file-alt fa-2x mb-2"></i>
                <h6 class="card-title">本周记录</h6>
                <h3 class="card-text">{{ total_records }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card bg-success">
            <div class="card-body text-center">
                <i class="fas fa-smile fa-2x mb-2"></i>
                <h6 class="card-title">正面情绪</h6>
                <h3 class="card-text">{{ emotion_stats.get('高兴', 0) + emotion_stats.get('喜好', 0) }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card bg-warning">
            <div class="card-body text-center">
                <i class="fas fa-frown fa-2x mb-2"></i>
                <h6 class="card-title">负面情绪</h6>
                <h3 class="card-text">{{ emotion_stats.get('悲伤', 0) + emotion_stats.get('愤怒', 0) + emotion_stats.get('恐惧', 0) + emotion_stats.get('厌恶', 0) }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card bg-danger">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h6 class="card-title">待处理预警</h6>
                <h3 class="card-text">{{ alerts|length }}</h3>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 情绪分布图表 -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie text-primary"></i> 本周情绪分布
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="emotionChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近预警 -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bell text-warning"></i> 最近预警
                </h5>
            </div>
            <div class="card-body">
                {% if alerts %}
                    {% for alert in alerts %}
                    <div class="alert-item alert-{{ alert.alert_level }} mb-2">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <small class="text-muted">{{ alert.created_at.strftime('%m-%d %H:%M') }}</small>
                                <p class="mb-1 small">{{ alert.alert_message }}</p>
                                <span class="badge bg-{{ 'danger' if alert.alert_level == 'high' else 'warning' if alert.alert_level == 'medium' else 'info' }}">
                                    {{ alert.alert_level }}
                                </span>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                    <div class="text-center mt-3">
                        <a href="{{ url_for('emotion.alerts') }}" class="btn btn-outline-primary btn-sm">
                            查看全部预警
                        </a>
                    </div>
                {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <p>暂无预警信息</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 最近记录 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history text-primary"></i> 最近记录
                </h5>
            </div>
            <div class="card-body">
                {% if recent_records %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>文本内容</th>
                                <th>预测情绪</th>
                                <th>置信度</th>
                                <th>来源</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in recent_records %}
                            <tr>
                                <td>{{ record.created_at.strftime('%m-%d %H:%M') }}</td>
                                <td>
                                    <span class="text-truncate d-inline-block" style="max-width: 200px;">
                                        {{ record.text_content }}
                                    </span>
                                </td>
                                <td>
                                    <span class="emotion-label emotion-{{ record.predicted_emotion }}">
                                        {{ record.predicted_emotion }}
                                    </span>
                                </td>
                                <td>{{ "%.1f"|format(record.confidence_score * 100) }}%</td>
                                <td>
                                    <span class="badge bg-secondary">{{ record.source_type }}</span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('main.history') }}" class="btn btn-outline-primary">
                        查看全部记录
                    </a>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <h5>暂无记录</h5>
                    <p>开始您的第一次情绪分析吧！</p>
                    <a href="{{ url_for('main.analyze') }}" class="btn btn-primary">
                        <i class="fas fa-brain"></i> 开始分析
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    // 情绪分布饼图
    var ctx = document.getElementById('emotionChart').getContext('2d');
    var emotionData = {{ emotion_stats|tojson }};
    
    var labels = Object.keys(emotionData);
    var data = Object.values(emotionData);
    var colors = labels.map(emotion => window.chartColors.emotions[emotion] || '#6c757d');
    
    if (labels.length > 0) {
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: colors,
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var total = context.dataset.data.reduce((a, b) => a + b, 0);
                                var percentage = ((context.parsed / total) * 100).toFixed(1);
                                return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    } else {
        // 如果没有数据，显示提示信息
        ctx.canvas.parentNode.innerHTML = '<div class="text-center text-muted py-4"><i class="fas fa-chart-pie fa-3x mb-3"></i><h5>暂无数据</h5><p>开始记录您的情绪吧！</p></div>';
    }
});
</script>
{% endblock %}
