{% extends "base.html" %}

{% block title %}管理员首页 - 黔南民族中学学生情绪分析系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-cog text-primary"></i> 
            系统管理中心
        </h2>
    </div>
</div>

<!-- 系统统计概览 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card dashboard-card bg-primary">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h6 class="card-title">总用户数</h6>
                <h3 class="card-text">{{ total_users }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card bg-success">
            <div class="card-body text-center">
                <i class="fas fa-user-graduate fa-2x mb-2"></i>
                <h6 class="card-title">学生用户</h6>
                <h3 class="card-text">{{ total_students }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card bg-info">
            <div class="card-body text-center">
                <i class="fas fa-chalkboard-teacher fa-2x mb-2"></i>
                <h6 class="card-title">教师用户</h6>
                <h3 class="card-text">{{ total_teachers }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card bg-warning">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h6 class="card-title">待处理预警</h6>
                <h3 class="card-text">{{ pending_alerts }}</h3>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 最近7天情绪记录统计 -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line text-primary"></i> 最近7天情绪记录统计
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center mb-3">
                    <div class="col-md-4">
                        <div class="border-end">
                            <h4 class="text-primary">{{ recent_records }}</h4>
                            <small class="text-muted">总记录数</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="border-end">
                            <h4 class="text-success">{{ (recent_records * 0.6)|int }}</h4>
                            <small class="text-muted">正面情绪</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h4 class="text-danger">{{ (recent_records * 0.4)|int }}</h4>
                        <small class="text-muted">负面情绪</small>
                    </div>
                </div>
                <div class="chart-container" style="height: 300px;">
                    <canvas id="weeklyStatsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt text-primary"></i> 快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('admin.users') }}" class="btn btn-outline-primary">
                        <i class="fas fa-users"></i> 用户管理
                    </a>
                    <a href="{{ url_for('admin.alerts') }}" class="btn btn-outline-warning">
                        <i class="fas fa-exclamation-triangle"></i> 预警管理
                    </a>
                    <a href="{{ url_for('admin.reports') }}" class="btn btn-outline-info">
                        <i class="fas fa-chart-bar"></i> 系统报告
                    </a>
                    <a href="{{ url_for('emotion.statistics') }}" class="btn btn-outline-success">
                        <i class="fas fa-analytics"></i> 情绪统计
                    </a>
                </div>
            </div>
        </div>

        <!-- 系统状态 -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-server text-primary"></i> 系统状态
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="mb-3">
                            <i class="fas fa-database fa-2x text-success"></i>
                            <div class="mt-2">
                                <small class="text-success">数据库</small><br>
                                <small class="text-muted">正常</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="mb-3">
                            <i class="fas fa-brain fa-2x text-success"></i>
                            <div class="mt-2">
                                <small class="text-success">AI模型</small><br>
                                <small class="text-muted">正常</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近预警 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-bell text-warning"></i> 最近预警
                    </h5>
                    <a href="{{ url_for('admin.alerts') }}" class="btn btn-sm btn-outline-primary">
                        查看全部
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if recent_alerts %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>学生</th>
                                <th>预警类型</th>
                                <th>级别</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for alert in recent_alerts %}
                            <tr>
                                <td>
                                    <small>{{ alert.created_at.strftime('%m-%d %H:%M') }}</small>
                                </td>
                                <td>{{ alert.user.real_name if alert.user else '未知用户' }}</td>
                                <td>
                                    <span class="badge bg-info">{{ alert.alert_type }}</span>
                                </td>
                                <td>
                                    {% if alert.alert_level == 'high' %}
                                        <span class="badge bg-danger">高</span>
                                    {% elif alert.alert_level == 'medium' %}
                                        <span class="badge bg-warning">中</span>
                                    {% else %}
                                        <span class="badge bg-info">低</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if alert.status == 'pending' %}
                                        <span class="badge bg-danger">待处理</span>
                                    {% elif alert.status == 'processing' %}
                                        <span class="badge bg-warning">处理中</span>
                                    {% else %}
                                        <span class="badge bg-success">已解决</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('admin.alerts', alert_id=alert.id) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-3">
                    <i class="fas fa-shield-alt fa-3x mb-3"></i>
                    <p>暂无预警信息</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    // 模拟周统计数据
    var ctx = document.getElementById('weeklyStatsChart').getContext('2d');
    
    // 生成最近7天的日期
    var dates = [];
    var positiveData = [];
    var negativeData = [];
    
    for (var i = 6; i >= 0; i--) {
        var date = new Date();
        date.setDate(date.getDate() - i);
        dates.push(date.toLocaleDateString());
        
        // 模拟数据
        positiveData.push(Math.floor(Math.random() * 20) + 10);
        negativeData.push(Math.floor(Math.random() * 15) + 5);
    }
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: dates,
            datasets: [{
                label: '正面情绪',
                data: positiveData,
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4
            }, {
                label: '负面情绪',
                data: negativeData,
                borderColor: '#dc3545',
                backgroundColor: 'rgba(220, 53, 69, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });
});
</script>
{% endblock %}
