#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户认证视图
"""

from flask import Blueprint, render_template, request, flash, redirect, url_for
from flask_login import login_user, logout_user, login_required, current_user
from app import db
from app.models.user import User
import logging

bp = Blueprint('auth', __name__)

@bp.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    if request.method == 'POST':
        try:
            username = request.form.get('username', '').strip()
            password = request.form.get('password', '')
            remember_me = bool(request.form.get('remember_me'))
            
            if not username or not password:
                flash('请输入用户名和密码', 'error')
                return render_template('auth/login.html')
            
            # 查找用户（支持用户名或邮箱登录）
            user = User.query.filter(
                (User.username == username) | (User.email == username)
            ).first()
            
            if user and user.check_password(password):
                if not user.is_active:
                    flash('账户已被禁用，请联系管理员', 'error')
                    return render_template('auth/login.html')
                
                login_user(user, remember=remember_me)
                flash(f'欢迎回来，{user.real_name}！', 'success')
                
                # 重定向到原来要访问的页面
                next_page = request.args.get('next')
                if next_page:
                    return redirect(next_page)
                return redirect(url_for('main.dashboard'))
            else:
                flash('用户名或密码错误', 'error')
                
        except Exception as e:
            logging.error(f"登录失败: {str(e)}")
            flash('登录失败，请稍后重试', 'error')
    
    return render_template('auth/login.html')

@bp.route('/register', methods=['GET', 'POST'])
def register():
    """用户注册"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    if request.method == 'POST':
        try:
            # 获取表单数据
            username = request.form.get('username', '').strip()
            email = request.form.get('email', '').strip()
            password = request.form.get('password', '')
            confirm_password = request.form.get('confirm_password', '')
            real_name = request.form.get('real_name', '').strip()
            user_type = request.form.get('user_type', 'student')
            
            # 基础验证
            if not all([username, email, password, confirm_password, real_name]):
                flash('请填写所有必填字段', 'error')
                return render_template('auth/register.html')
            
            if password != confirm_password:
                flash('两次输入的密码不一致', 'error')
                return render_template('auth/register.html')
            
            if len(password) < 6:
                flash('密码长度至少6位', 'error')
                return render_template('auth/register.html')
            
            # 检查用户名和邮箱是否已存在
            if User.query.filter_by(username=username).first():
                flash('用户名已存在', 'error')
                return render_template('auth/register.html')
            
            if User.query.filter_by(email=email).first():
                flash('邮箱已被注册', 'error')
                return render_template('auth/register.html')
            
            # 创建新用户
            user = User(
                username=username,
                email=email,
                real_name=real_name,
                user_type=user_type
            )
            user.set_password(password)
            
            # 根据用户类型设置额外字段
            if user_type == 'student':
                user.student_id = request.form.get('student_id', '').strip()
                user.class_name = request.form.get('class_name', '').strip()
                user.grade = request.form.get('grade', '').strip()
                
                # 检查学号是否已存在
                if user.student_id and User.query.filter_by(student_id=user.student_id).first():
                    flash('学号已存在', 'error')
                    return render_template('auth/register.html')
                    
            elif user_type == 'teacher':
                user.teacher_id = request.form.get('teacher_id', '').strip()
                user.subject = request.form.get('subject', '').strip()
                
                # 检查教师工号是否已存在
                if user.teacher_id and User.query.filter_by(teacher_id=user.teacher_id).first():
                    flash('教师工号已存在', 'error')
                    return render_template('auth/register.html')
            
            user.phone = request.form.get('phone', '').strip()
            
            db.session.add(user)
            db.session.commit()
            
            flash('注册成功！请登录', 'success')
            return redirect(url_for('auth.login'))
            
        except Exception as e:
            logging.error(f"注册失败: {str(e)}")
            flash('注册失败，请稍后重试', 'error')
            db.session.rollback()
    
    return render_template('auth/register.html')

@bp.route('/logout')
@login_required
def logout():
    """用户登出"""
    logout_user()
    flash('您已成功登出', 'info')
    return redirect(url_for('main.index'))

@bp.route('/profile')
@login_required
def profile():
    """个人资料页面"""
    try:
        # 获取情绪统计数据
        emotion_records = EmotionRecord.query.filter_by(user_id=current_user.id).all()

        emotion_stats = {
            'total_records': len(emotion_records),
            'positive_count': len([r for r in emotion_records if r.predicted_emotion in ['高兴', '喜好']]),
            'negative_count': len([r for r in emotion_records if r.is_negative]),
            'emotion_distribution': {}
        }

        # 计算情绪分布
        for record in emotion_records:
            emotion = record.predicted_emotion
            emotion_stats['emotion_distribution'][emotion] = emotion_stats['emotion_distribution'].get(emotion, 0) + 1

        # 获取最近的记录
        recent_records = EmotionRecord.query.filter_by(user_id=current_user.id)\
            .order_by(EmotionRecord.created_at.desc()).limit(5).all()

        return render_template('auth/profile.html',
                             emotion_stats=emotion_stats,
                             recent_records=recent_records)

    except Exception as e:
        logging.error(f"个人资料页面加载失败: {str(e)}")
        flash('页面加载失败，请稍后重试', 'error')
        return render_template('auth/profile.html',
                             emotion_stats={'total_records': 0, 'positive_count': 0, 'negative_count': 0, 'emotion_distribution': {}},
                             recent_records=[])

@bp.route('/profile/edit', methods=['GET', 'POST'])
@login_required
def edit_profile():
    """编辑用户资料"""
    if request.method == 'POST':
        try:
            current_user.real_name = request.form.get('real_name', '').strip()
            current_user.email = request.form.get('email', '').strip()
            current_user.phone = request.form.get('phone', '').strip()
            
            # 根据用户类型更新特定字段
            if current_user.is_student():
                current_user.class_name = request.form.get('class_name', '').strip()
                current_user.grade = request.form.get('grade', '').strip()
            elif current_user.is_teacher():
                current_user.subject = request.form.get('subject', '').strip()
            
            db.session.commit()
            flash('资料更新成功', 'success')
            return redirect(url_for('auth.profile'))
            
        except Exception as e:
            logging.error(f"资料更新失败: {str(e)}")
            flash('资料更新失败，请稍后重试', 'error')
            db.session.rollback()

    # 创建表单对象（简化版）
    form_data = {
        'real_name': current_user.real_name or '',
        'email': current_user.email or '',
        'student_id': current_user.student_id or '' if current_user.is_student() else '',
        'class_name': current_user.class_name or '' if current_user.is_student() else ''
    }

    return render_template('auth/edit_profile.html', form=type('Form', (), form_data)())

@bp.route('/change_password', methods=['POST'])
@login_required
def change_password():
    """修改密码"""
    try:
        data = request.form.to_dict() if not request.is_json else request.get_json()

        current_password = data.get('current_password')
        new_password = data.get('new_password')
        confirm_password = data.get('confirm_password')

        # 验证当前密码
        if not current_user.check_password(current_password):
            if request.is_json or 'application/json' in request.headers.get('Accept', ''):
                return jsonify({'success': False, 'message': '当前密码错误'}), 400
            else:
                flash('当前密码错误', 'error')
                return redirect(url_for('auth.edit_profile'))

        # 验证新密码
        if len(new_password) < 6:
            if request.is_json or 'application/json' in request.headers.get('Accept', ''):
                return jsonify({'success': False, 'message': '密码长度至少6位'}), 400
            else:
                flash('密码长度至少6位', 'error')
                return redirect(url_for('auth.edit_profile'))

        if new_password != confirm_password:
            if request.is_json or 'application/json' in request.headers.get('Accept', ''):
                return jsonify({'success': False, 'message': '两次输入的密码不一致'}), 400
            else:
                flash('两次输入的密码不一致', 'error')
                return redirect(url_for('auth.edit_profile'))

        # 更新密码
        current_user.set_password(new_password)
        db.session.commit()

        if request.is_json or 'application/json' in request.headers.get('Accept', ''):
            return jsonify({'success': True, 'message': '密码修改成功'})
        else:
            flash('密码修改成功', 'success')
            return redirect(url_for('auth.profile'))

    except Exception as e:
        logging.error(f"修改密码失败: {str(e)}")
        db.session.rollback()
        if request.is_json or 'application/json' in request.headers.get('Accept', ''):
            return jsonify({'success': False, 'message': str(e)}), 500
        else:
            flash('密码修改失败，请稍后重试', 'error')
            return redirect(url_for('auth.edit_profile'))
