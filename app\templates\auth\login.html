{% extends "base.html" %}

{% block title %}登录 - 黔南民族中学学生情绪分析系统{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-body p-4">
                <div class="text-center mb-4">
                    <i class="fas fa-heart fa-3x text-primary mb-3"></i>
                    <h4 class="card-title">用户登录</h4>
                    <p class="text-muted">黔南民族中学学生情绪分析系统</p>
                </div>

                <form method="POST">
                    <div class="mb-3">
                        <label for="username" class="form-label">用户名或邮箱</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="username" name="username" 
                                   placeholder="请输入用户名或邮箱" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">密码</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="password" name="password" 
                                   placeholder="请输入密码" required>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                        <label class="form-check-label" for="remember_me">
                            记住我
                        </label>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt"></i> 登录
                        </button>
                    </div>
                </form>

                <hr class="my-4">

                <div class="text-center">
                    <p class="mb-2">还没有账户？</p>
                    <a href="{{ url_for('auth.register') }}" class="btn btn-outline-primary">
                        <i class="fas fa-user-plus"></i> 立即注册
                    </a>
                </div>

                <!-- 默认账户提示 -->
                <div class="alert alert-info mt-4">
                    <h6><i class="fas fa-info-circle"></i> 默认测试账户</h6>
                    <small>
                        <strong>管理员:</strong> admin / admin123<br>
                        <strong>教师:</strong> teacher001 / teacher123<br>
                        <strong>学生:</strong> student001 / student123
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    // 表单验证
    $('form').on('submit', function(e) {
        var username = $('#username').val().trim();
        var password = $('#password').val();
        
        if (!username || !password) {
            e.preventDefault();
            alert('请填写用户名和密码');
            return false;
        }
        
        // 显示加载状态
        var submitBtn = $(this).find('button[type="submit"]');
        var originalText = submitBtn.html();
        submitBtn.html('<span class="loading-spinner"></span> 登录中...');
        submitBtn.prop('disabled', true);
        
        // 如果登录失败，恢复按钮状态
        setTimeout(function() {
            submitBtn.html(originalText);
            submitBtn.prop('disabled', false);
        }, 3000);
    });
    
    // 回车键提交
    $('#password').on('keypress', function(e) {
        if (e.which === 13) {
            $('form').submit();
        }
    });
});
</script>
{% endblock %}
