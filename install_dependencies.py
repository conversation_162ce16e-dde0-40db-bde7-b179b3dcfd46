#!/usr/bin/env python3
"""
依赖安装脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        print(f"正在安装 {package}...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                              capture_output=True, text=True, check=True)
        print(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {package} 安装失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 安装 {package} 时出错: {e}")
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package.replace('-', '_'))
        return True
    except ImportError:
        return False

def main():
    """主函数"""
    print("黔南民族中学学生情绪分析系统 - 依赖安装")
    print("=" * 50)
    
    # 基础依赖列表
    basic_packages = [
        'flask',
        'flask-sqlalchemy', 
        'flask-login',
        'flask-wtf',
        'pymysql',
        'python-dotenv',
        'werkzeug'
    ]
    
    # 可选依赖（用于完整功能）
    optional_packages = [
        'transformers',
        'torch',
        'numpy',
        'pandas',
        'scikit-learn'
    ]
    
    print("1. 安装基础依赖...")
    basic_success = 0
    for package in basic_packages:
        if install_package(package):
            basic_success += 1
    
    print(f"\n基础依赖安装完成: {basic_success}/{len(basic_packages)}")
    
    if basic_success == len(basic_packages):
        print("✓ 所有基础依赖安装成功！应用可以正常运行。")
        
        # 询问是否安装可选依赖
        install_optional = input("\n是否安装可选依赖（用于完整的AI功能）？(y/n): ").lower().strip()
        
        if install_optional == 'y':
            print("\n2. 安装可选依赖...")
            optional_success = 0
            for package in optional_packages:
                if install_package(package):
                    optional_success += 1
            
            print(f"\n可选依赖安装完成: {optional_success}/{len(optional_packages)}")
    else:
        print("✗ 部分基础依赖安装失败，请检查网络连接或Python环境。")
    
    print("\n" + "=" * 50)
    print("安装完成！")
    print("\n下一步:")
    print("1. 运行 'python app.py' 启动完整应用")
    print("2. 或运行 'python test_app.py' 启动测试版本")
    print("3. 访问 http://127.0.0.1:5000 查看应用")

if __name__ == '__main__':
    main()
