#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统测试脚本
用于验证情绪分析系统的基本功能
"""

import os
import sys
from app import create_app, db
from config import Config
from app.models.user import User
from app.models.emotion import EmotionRecord, EmotionDetail
from app.utils.emotion_analyzer import emotion_analyzer

def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    try:
        app = create_app(Config)
        with app.app_context():
            # 尝试查询用户表
            user_count = User.query.count()
            print(f"✅ 数据库连接成功，当前用户数量: {user_count}")
            return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_model_loading():
    """测试模型加载"""
    print("🔍 测试情绪分析模型加载...")
    try:
        if emotion_analyzer.load_model():
            print("✅ 情绪分析模型加载成功")
            return True
        else:
            print("❌ 情绪分析模型加载失败")
            return False
    except Exception as e:
        print(f"❌ 模型加载异常: {e}")
        return False

def test_emotion_analysis():
    """测试情绪分析功能"""
    print("🔍 测试情绪分析功能...")
    
    test_texts = [
        "今天心情很好，阳光明媚！",
        "我很生气，这太不公平了！",
        "我感到很害怕，不知道该怎么办。",
        "这个消息让我很惊讶。",
        "我对这件事感到厌恶。"
    ]
    
    try:
        for i, text in enumerate(test_texts, 1):
            print(f"  测试文本 {i}: {text}")
            result = emotion_analyzer.predict(text)
            print(f"    预测情绪: {result['predicted_emotion']}")
            print(f"    置信度: {result['confidence']:.3f}")
            print(f"    是否负面: {result['is_negative']}")
            print()
        
        print("✅ 情绪分析功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 情绪分析功能测试失败: {e}")
        return False

def test_user_creation():
    """测试用户创建功能"""
    print("🔍 测试用户创建功能...")
    try:
        app = create_app(Config)
        with app.app_context():
            # 检查是否已存在测试用户
            test_user = User.query.filter_by(username='test_user').first()
            if test_user:
                db.session.delete(test_user)
                db.session.commit()
            
            # 创建测试用户
            user = User(
                username='test_user',
                email='<EMAIL>',
                real_name='测试用户',
                user_type='student',
                student_id='TEST001',
                class_name='测试班级',
                grade='高一'
            )
            user.set_password('test123')
            
            db.session.add(user)
            db.session.commit()
            
            # 验证用户创建成功
            created_user = User.query.filter_by(username='test_user').first()
            if created_user and created_user.check_password('test123'):
                print("✅ 用户创建功能测试通过")
                
                # 清理测试数据
                db.session.delete(created_user)
                db.session.commit()
                return True
            else:
                print("❌ 用户验证失败")
                return False
                
    except Exception as e:
        print(f"❌ 用户创建功能测试失败: {e}")
        return False

def test_emotion_record_creation():
    """测试情绪记录创建功能"""
    print("🔍 测试情绪记录创建功能...")
    try:
        app = create_app(Config)
        with app.app_context():
            # 创建临时用户
            user = User(
                username='temp_user',
                email='<EMAIL>',
                real_name='临时用户',
                user_type='student'
            )
            user.set_password('temp123')
            db.session.add(user)
            db.session.flush()
            
            # 创建情绪记录
            record = EmotionRecord(
                user_id=user.id,
                text_content='测试文本内容',
                source_type='manual',
                predicted_emotion='高兴',
                confidence_score=0.85,
                is_negative=False
            )
            db.session.add(record)
            db.session.flush()
            
            # 创建情绪详细信息
            detail = EmotionDetail(
                emotion_record_id=record.id,
                emotion_type='高兴',
                probability=0.85
            )
            db.session.add(detail)
            db.session.commit()
            
            # 验证记录创建成功
            created_record = EmotionRecord.query.filter_by(user_id=user.id).first()
            if created_record:
                print("✅ 情绪记录创建功能测试通过")
                
                # 清理测试数据
                db.session.delete(detail)
                db.session.delete(record)
                db.session.delete(user)
                db.session.commit()
                return True
            else:
                print("❌ 情绪记录验证失败")
                return False
                
    except Exception as e:
        print(f"❌ 情绪记录创建功能测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始系统功能测试")
    print("=" * 50)
    
    tests = [
        ("数据库连接", test_database_connection),
        ("模型加载", test_model_loading),
        ("情绪分析", test_emotion_analysis),
        ("用户创建", test_user_creation),
        ("情绪记录创建", test_emotion_record_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        if test_func():
            passed += 1
        print("-" * 30)
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关配置。")
        return False

if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)
