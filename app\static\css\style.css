/* 黔南民族中学学生情绪分析系统 - 自定义样式 */

/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
    background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.3rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: #fff !important;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
}

/* 情绪标签样式 */
.emotion-label {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    margin: 0.25rem;
}

.emotion-happy { background-color: #d4edda; color: #155724; }
.emotion-sad { background-color: #f8d7da; color: #721c24; }
.emotion-angry { background-color: #f5c6cb; color: #721c24; }
.emotion-fear { background-color: #d1ecf1; color: #0c5460; }
.emotion-disgust { background-color: #ffeaa7; color: #856404; }
.emotion-like { background-color: #d1f2eb; color: #0c5460; }
.emotion-surprise { background-color: #e2e3e5; color: #383d41; }

/* 统计图表容器 */
.chart-container {
    position: relative;
    height: 400px;
    margin: 2rem 0;
}

/* 预警样式 */
.alert-item {
    border-left: 4px solid;
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 0 8px 8px 0;
}

.alert-low { border-left-color: #28a745; }
.alert-medium { border-left-color: #ffc107; }
.alert-high { border-left-color: #dc3545; }

/* 用户头像 */
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

/* 情绪分析结果 */
.emotion-result {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin: 2rem 0;
}

.emotion-score {
    font-size: 2.5rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.confidence-bar {
    height: 8px;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    overflow: hidden;
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 4px;
    transition: width 0.8s ease;
}

/* 情绪详细分数 */
.emotion-details {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1rem;
}

.emotion-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.emotion-item:last-child {
    border-bottom: none;
}

.emotion-bar {
    width: 100px;
    height: 6px;
    background-color: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.emotion-bar-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.6s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chart-container {
        height: 300px;
    }
    
    .emotion-result {
        padding: 1.5rem;
    }
    
    .emotion-score {
        font-size: 2rem;
    }
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 表格样式 */
.table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

/* 分页样式 */
.pagination {
    justify-content: center;
}

.page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: none;
    color: #007bff;
}

.page-item.active .page-link {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

/* 首页特色样式 */
.hero-section {
    padding: 4rem 0;
}

.feature-icon {
    width: 4rem;
    height: 4rem;
    font-size: 2rem;
}

.step-number {
    width: 3rem;
    height: 3rem;
    font-weight: bold;
    font-size: 1.2rem;
}

/* 仪表板样式 */
.dashboard-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.dashboard-card .card-title {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.dashboard-card .card-text {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 0;
}

/* 情绪趋势图 */
.trend-up {
    color: #28a745;
}

.trend-down {
    color: #dc3545;
}

.trend-stable {
    color: #6c757d;
}
