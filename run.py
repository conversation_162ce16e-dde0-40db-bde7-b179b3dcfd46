#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
黔南民族中学学生情绪分析系统启动脚本
"""

import os
import sys
from app import create_app, db
from config import Config
from app.utils.emotion_analyzer import emotion_analyzer

def create_database():
    """创建数据库表"""
    try:
        with app.app_context():
            db.create_all()
            print("✅ 数据库表创建成功")
    except Exception as e:
        print(f"❌ 数据库表创建失败: {e}")
        return False
    return True

def load_emotion_model():
    """加载情绪分析模型"""
    try:
        print("🔄 正在加载情绪分析模型...")
        if emotion_analyzer.load_model():
            print("✅ 情绪分析模型加载成功")
            return True
        else:
            print("❌ 情绪分析模型加载失败")
            return False
    except Exception as e:
        print(f"❌ 模型加载异常: {e}")
        return False

def check_requirements():
    """检查系统要求"""
    print("🔍 检查系统要求...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本需要3.8或更高")
        return False
    print(f"✅ Python版本: {sys.version}")
    
    # 检查模型文件
    model_path = Config.MODEL_PATH
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return False
    
    required_files = ['config.json', 'pytorch_model.bin', 'vocab.txt']
    for file in required_files:
        file_path = os.path.join(model_path, file)
        if not os.path.exists(file_path):
            print(f"❌ 缺少模型文件: {file}")
            return False
    print("✅ 模型文件检查通过")
    
    # 检查上传目录
    upload_folder = Config.UPLOAD_FOLDER
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder)
        print(f"✅ 创建上传目录: {upload_folder}")
    
    return True

def main():
    """主函数"""
    print("🚀 启动黔南民族中学学生情绪分析系统")
    print("=" * 50)
    
    # 检查系统要求
    if not check_requirements():
        print("❌ 系统要求检查失败，请解决上述问题后重试")
        sys.exit(1)
    
    # 创建Flask应用
    global app
    app = create_app(Config)
    
    # 创建数据库表
    if not create_database():
        print("❌ 数据库初始化失败，请检查数据库配置")
        sys.exit(1)
    
    # 加载情绪分析模型
    if not load_emotion_model():
        print("⚠️  模型加载失败，情绪分析功能可能无法正常使用")
        print("   请检查模型文件是否完整")
    
    print("=" * 50)
    print("🎉 系统启动成功！")
    print(f"📍 访问地址: http://localhost:5000")
    print(f"👤 默认管理员: admin / admin123")
    print("⚠️  首次登录请立即修改默认密码")
    print("=" * 50)
    
    # 启动应用
    try:
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            use_reloader=False  # 避免重复加载模型
        )
    except KeyboardInterrupt:
        print("\n👋 系统已停止运行")
    except Exception as e:
        print(f"❌ 系统运行异常: {e}")

if __name__ == '__main__':
    main()
