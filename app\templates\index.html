{% extends "base.html" %}

{% block title %}首页 - 黔南民族中学学生情绪分析系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto text-center">
        <!-- 系统介绍 -->
        <div class="hero-section mb-5">
            <h1 class="display-4 text-primary mb-4">
                <i class="fas fa-heart text-danger"></i>
                黔南民族中学学生情绪分析系统
            </h1>
            <p class="lead text-muted mb-4">
                基于先进的StructBERT深度学习模型，为学生心理健康提供智能化分析与预警服务
            </p>
            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                <a href="{{ url_for('auth.login') }}" class="btn btn-primary btn-lg me-md-2">
                    <i class="fas fa-sign-in-alt"></i> 立即登录
                </a>
                <a href="{{ url_for('auth.register') }}" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-user-plus"></i> 注册账户
                </a>
            </div>
        </div>

        <!-- 功能特色 -->
        <div class="row g-4 mb-5">
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="feature-icon bg-primary bg-gradient text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 4rem; height: 4rem;">
                            <i class="fas fa-brain fa-2x"></i>
                        </div>
                        <h5 class="card-title">智能情绪识别</h5>
                        <p class="card-text text-muted">
                            采用StructBERT模型，准确识别恐惧、愤怒、厌恶、喜好、悲伤、高兴、惊讶七种情绪类型
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="feature-icon bg-success bg-gradient text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 4rem; height: 4rem;">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                        <h5 class="card-title">趋势分析</h5>
                        <p class="card-text text-muted">
                            实时监测学生情绪变化趋势，提供直观的数据可视化图表和统计分析
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="feature-icon bg-warning bg-gradient text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 4rem; height: 4rem;">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                        <h5 class="card-title">智能预警</h5>
                        <p class="card-text text-muted">
                            自动检测负面情绪，及时向教师和管理员发送预警，确保学生心理健康
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统优势 -->
        <div class="row align-items-center mb-5">
            <div class="col-lg-6">
                <h3 class="text-primary mb-3">系统优势</h3>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        支持中文文本情绪分析，适合中国学生使用场景
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        多角色权限管理，学生、教师、管理员分级访问
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        实时数据分析与可视化展示
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        智能预警系统，及时发现心理健康问题
                    </li>
                </ul>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <img src="{{ url_for('static', filename='images/emotion-analysis.png') }}" 
                         alt="情绪分析示意图" 
                         class="img-fluid rounded shadow"
                         onerror="this.style.display='none'">
                </div>
            </div>
        </div>

        <!-- 使用流程 -->
        <div class="mb-5">
            <h3 class="text-primary mb-4">使用流程</h3>
            <div class="row g-3">
                <div class="col-md-3">
                    <div class="text-center">
                        <div class="step-number bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 3rem; height: 3rem;">
                            1
                        </div>
                        <h6>注册登录</h6>
                        <p class="text-muted small">创建账户并登录系统</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <div class="step-number bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 3rem; height: 3rem;">
                            2
                        </div>
                        <h6>输入文本</h6>
                        <p class="text-muted small">输入日记、心情或对话内容</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <div class="step-number bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 3rem; height: 3rem;">
                            3
                        </div>
                        <h6>智能分析</h6>
                        <p class="text-muted small">AI模型自动分析情绪类型</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <div class="step-number bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 3rem; height: 3rem;">
                            4
                        </div>
                        <h6>查看结果</h6>
                        <p class="text-muted small">获得详细的情绪分析报告</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
