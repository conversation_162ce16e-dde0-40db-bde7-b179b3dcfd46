{% extends "base.html" %}

{% block title %}注册 - 黔南民族中学学生情绪分析系统{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow">
            <div class="card-body p-4">
                <div class="text-center mb-4">
                    <i class="fas fa-user-plus fa-3x text-primary mb-3"></i>
                    <h4 class="card-title">用户注册</h4>
                    <p class="text-muted">加入黔南民族中学学生情绪分析系统</p>
                </div>

                <form method="POST" id="register-form">
                    <!-- 基础信息 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名 *</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       placeholder="请输入用户名" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="real_name" class="form-label">真实姓名 *</label>
                                <input type="text" class="form-control" id="real_name" name="real_name" 
                                       placeholder="请输入真实姓名" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">邮箱地址 *</label>
                        <input type="email" class="form-control" id="email" name="email" 
                               placeholder="请输入邮箱地址" required>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">密码 *</label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="请输入密码（至少6位）" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">确认密码 *</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                       placeholder="请再次输入密码" required>
                            </div>
                        </div>
                    </div>

                    <!-- 用户类型选择 -->
                    <div class="mb-3">
                        <label for="user_type" class="form-label">用户类型 *</label>
                        <select class="form-select" id="user_type" name="user_type" required>
                            <option value="">请选择用户类型</option>
                            <option value="student">学生</option>
                            <option value="teacher">教师</option>
                        </select>
                    </div>

                    <!-- 学生特有字段 -->
                    <div id="student-fields" style="display: none;">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="student_id" class="form-label">学号</label>
                                    <input type="text" class="form-control" id="student_id" name="student_id" 
                                           placeholder="请输入学号">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="grade" class="form-label">年级</label>
                                    <select class="form-select" id="grade" name="grade">
                                        <option value="">请选择年级</option>
                                        <option value="高一">高一</option>
                                        <option value="高二">高二</option>
                                        <option value="高三">高三</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="class_name" class="form-label">班级</label>
                                    <input type="text" class="form-control" id="class_name" name="class_name" 
                                           placeholder="如：高三(1)班">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 教师特有字段 -->
                    <div id="teacher-fields" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="teacher_id" class="form-label">教师工号</label>
                                    <input type="text" class="form-control" id="teacher_id" name="teacher_id" 
                                           placeholder="请输入教师工号">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="subject" class="form-label">任教科目</label>
                                    <input type="text" class="form-control" id="subject" name="subject" 
                                           placeholder="如：语文、数学、英语">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 联系方式 -->
                    <div class="mb-3">
                        <label for="phone" class="form-label">手机号码</label>
                        <input type="tel" class="form-control" id="phone" name="phone" 
                               placeholder="请输入手机号码（可选）">
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-user-plus"></i> 注册账户
                        </button>
                    </div>
                </form>

                <hr class="my-4">

                <div class="text-center">
                    <p class="mb-2">已有账户？</p>
                    <a href="{{ url_for('auth.login') }}" class="btn btn-outline-primary">
                        <i class="fas fa-sign-in-alt"></i> 立即登录
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    // 用户类型切换
    $('#user_type').on('change', function() {
        var userType = $(this).val();
        
        $('#student-fields, #teacher-fields').hide();
        
        if (userType === 'student') {
            $('#student-fields').show();
        } else if (userType === 'teacher') {
            $('#teacher-fields').show();
        }
    });
    
    // 表单验证
    $('#register-form').on('submit', function(e) {
        var password = $('#password').val();
        var confirmPassword = $('#confirm_password').val();
        
        // 密码长度验证
        if (password.length < 6) {
            e.preventDefault();
            alert('密码长度至少6位');
            return false;
        }
        
        // 密码确认验证
        if (password !== confirmPassword) {
            e.preventDefault();
            alert('两次输入的密码不一致');
            return false;
        }
        
        // 用户类型验证
        if (!$('#user_type').val()) {
            e.preventDefault();
            alert('请选择用户类型');
            return false;
        }
        
        // 显示加载状态
        var submitBtn = $(this).find('button[type="submit"]');
        var originalText = submitBtn.html();
        submitBtn.html('<span class="loading-spinner"></span> 注册中...');
        submitBtn.prop('disabled', true);
        
        // 如果注册失败，恢复按钮状态
        setTimeout(function() {
            submitBtn.html(originalText);
            submitBtn.prop('disabled', false);
        }, 5000);
    });
    
    // 密码强度提示
    $('#password').on('input', function() {
        var password = $(this).val();
        var strength = 0;
        
        if (password.length >= 6) strength++;
        if (password.match(/[a-z]/)) strength++;
        if (password.match(/[A-Z]/)) strength++;
        if (password.match(/[0-9]/)) strength++;
        if (password.match(/[^a-zA-Z0-9]/)) strength++;
        
        // 可以添加密码强度显示
    });
    
    // 实时密码确认验证
    $('#confirm_password').on('input', function() {
        var password = $('#password').val();
        var confirmPassword = $(this).val();
        
        if (confirmPassword && password !== confirmPassword) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
});
</script>
{% endblock %}
