{% extends "base.html" %}

{% block title %}统计分析 - 黔南民族中学学生情绪分析系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-chart-bar text-primary"></i> 
                {% if current_user.is_student() %}
                    我的情绪统计
                {% else %}
                    学生情绪统计
                {% endif %}
            </h2>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="changeTimeRange(7)">
                    最近7天
                </button>
                <button type="button" class="btn btn-outline-primary active" onclick="changeTimeRange(30)">
                    最近30天
                </button>
                <button type="button" class="btn btn-outline-primary" onclick="changeTimeRange(90)">
                    最近90天
                </button>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-file-alt fa-2x text-primary mb-2"></i>
                        <h4 class="card-title">{{ analysis_result.total_records }}</h4>
                        <p class="card-text text-muted">总记录数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-smile fa-2x text-success mb-2"></i>
                        <h4 class="card-title">{{ analysis_result.positive_count }}</h4>
                        <p class="card-text text-muted">正面情绪</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-frown fa-2x text-danger mb-2"></i>
                        <h4 class="card-title">{{ analysis_result.negative_count }}</h4>
                        <p class="card-text text-muted">负面情绪</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-percentage fa-2x text-info mb-2"></i>
                        <h4 class="card-title">{{ "%.1f"|format(analysis_result.positive_ratio * 100) }}%</h4>
                        <p class="card-text text-muted">正面比例</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 情绪分布饼图 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie text-primary"></i> 情绪分布
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="emotionDistributionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 情绪趋势图 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line text-primary"></i> 情绪趋势
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="emotionTrendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细统计表格 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-table text-primary"></i> 详细统计
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>情绪类型</th>
                                        <th>记录数量</th>
                                        <th>占比</th>
                                        <th>平均置信度</th>
                                        <th>情绪类别</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for emotion, count in analysis_result.emotion_distribution.items() %}
                                    <tr>
                                        <td>
                                            <span class="emotion-label emotion-{{ emotion }}">
                                                {{ emotion }}
                                            </span>
                                        </td>
                                        <td>{{ count }}</td>
                                        <td>
                                            {% if analysis_result.total_records > 0 %}
                                                {{ "%.1f"|format((count / analysis_result.total_records) * 100) }}%
                                            {% else %}
                                                0%
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar" 
                                                     style="width: {{ (count / analysis_result.total_records * 100) if analysis_result.total_records > 0 else 0 }}%">
                                                    {{ "%.1f"|format((count / analysis_result.total_records) * 100) if analysis_result.total_records > 0 else 0 }}%
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            {% if emotion in ['悲伤', '愤怒', '恐惧', '厌恶'] %}
                                                <span class="badge bg-danger">负面</span>
                                            {% elif emotion in ['高兴', '喜好'] %}
                                                <span class="badge bg-success">正面</span>
                                            {% else %}
                                                <span class="badge bg-secondary">中性</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 情绪健康评估 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-heart text-primary"></i> 情绪健康评估
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>整体评估</h6>
                                {% set positive_ratio = analysis_result.positive_ratio %}
                                {% if positive_ratio >= 0.7 %}
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle"></i>
                                        <strong>情绪状态良好</strong><br>
                                        正面情绪占比较高，心理状态健康。
                                    </div>
                                {% elif positive_ratio >= 0.4 %}
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <strong>情绪状态一般</strong><br>
                                        建议关注情绪变化，适当调节心情。
                                    </div>
                                {% else %}
                                    <div class="alert alert-danger">
                                        <i class="fas fa-exclamation-circle"></i>
                                        <strong>需要关注</strong><br>
                                        负面情绪较多，建议寻求专业帮助。
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <h6>趋势分析</h6>
                                {% if analysis_result.trend == 'improving' %}
                                    <div class="alert alert-info">
                                        <i class="fas fa-arrow-up trend-up"></i>
                                        <strong>情绪趋势向好</strong><br>
                                        最近情绪状态有所改善。
                                    </div>
                                {% elif analysis_result.trend == 'declining' %}
                                    <div class="alert alert-warning">
                                        <i class="fas fa-arrow-down trend-down"></i>
                                        <strong>情绪趋势下降</strong><br>
                                        需要关注情绪变化。
                                    </div>
                                {% else %}
                                    <div class="alert alert-secondary">
                                        <i class="fas fa-minus trend-stable"></i>
                                        <strong>情绪趋势稳定</strong><br>
                                        情绪状态相对稳定。
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    // 情绪分布饼图
    var distributionCtx = document.getElementById('emotionDistributionChart').getContext('2d');
    var distributionData = {{ analysis_result.emotion_distribution|tojson }};
    
    var labels = Object.keys(distributionData);
    var data = Object.values(distributionData);
    var colors = labels.map(emotion => window.chartColors.emotions[emotion] || '#6c757d');
    
    if (labels.length > 0) {
        new Chart(distributionCtx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: colors,
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var total = context.dataset.data.reduce((a, b) => a + b, 0);
                                var percentage = ((context.parsed / total) * 100).toFixed(1);
                                return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    } else {
        distributionCtx.canvas.parentNode.innerHTML = '<div class="text-center text-muted py-4"><i class="fas fa-chart-pie fa-3x mb-3"></i><h5>暂无数据</h5></div>';
    }
    
    // 加载趋势图数据
    loadTrendChart();
});

function loadTrendChart() {
    var days = $('.btn-group .btn.active').text().match(/\d+/)[0];
    
    $.get('/emotion/api/statistics', {days: days})
        .done(function(response) {
            var trendCtx = document.getElementById('emotionTrendChart').getContext('2d');
            var dailyStats = response.daily_stats;
            
            // 准备数据
            var dates = Object.keys(dailyStats).sort();
            var positiveData = [];
            var negativeData = [];
            
            dates.forEach(function(date) {
                var stats = dailyStats[date];
                positiveData.push(stats.positive);
                negativeData.push(stats.negative);
            });
            
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: dates.map(date => new Date(date).toLocaleDateString()),
                    datasets: [{
                        label: '正面情绪',
                        data: positiveData,
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.4
                    }, {
                        label: '负面情绪',
                        data: negativeData,
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });
        })
        .fail(function() {
            document.getElementById('emotionTrendChart').parentNode.innerHTML = 
                '<div class="text-center text-muted py-4"><i class="fas fa-exclamation-triangle fa-3x mb-3"></i><h5>数据加载失败</h5></div>';
        });
}

function changeTimeRange(days) {
    // 更新按钮状态
    $('.btn-group .btn').removeClass('active');
    event.target.classList.add('active');
    
    // 重新加载页面
    window.location.href = '{{ url_for("emotion.statistics") }}?days=' + days;
}
</script>
{% endblock %}
