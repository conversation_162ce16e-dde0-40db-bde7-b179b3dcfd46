{% extends "base.html" %}

{% block title %}系统报告 - 黔南民族中学学生情绪分析系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-chart-bar text-primary"></i> 
                系统报告
            </h2>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="changeTimeRange(7)">
                    最近7天
                </button>
                <button type="button" class="btn btn-outline-primary active" onclick="changeTimeRange(30)">
                    最近30天
                </button>
                <button type="button" class="btn btn-outline-primary" onclick="changeTimeRange(90)">
                    最近90天
                </button>
            </div>
        </div>

        <!-- 系统概览 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-users fa-2x text-primary mb-2"></i>
                        <h4 class="card-title">{{ total_users }}</h4>
                        <p class="card-text text-muted">总用户数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-file-alt fa-2x text-success mb-2"></i>
                        <h4 class="card-title">{{ total_records }}</h4>
                        <p class="card-text text-muted">情绪记录</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                        <h4 class="card-title">{{ total_alerts }}</h4>
                        <p class="card-text text-muted">预警数量</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-calendar fa-2x text-info mb-2"></i>
                        <h4 class="card-title">{{ days }}天</h4>
                        <p class="card-text text-muted">统计周期</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 用户活跃度 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line text-primary"></i> 用户活跃度
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" style="height: 300px;">
                            <canvas id="userActivityChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 情绪分布 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie text-primary"></i> 整体情绪分布
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" style="height: 300px;">
                            <canvas id="emotionDistributionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细统计表格 -->
        <div class="row mt-4">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-table text-primary"></i> 用户统计详情
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>用户类型</th>
                                        <th>用户数量</th>
                                        <th>活跃用户</th>
                                        <th>情绪记录</th>
                                        <th>平均记录数</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <span class="badge bg-success">学生</span>
                                        </td>
                                        <td>{{ user_stats.students }}</td>
                                        <td>{{ user_stats.active_students }}</td>
                                        <td>{{ user_stats.student_records }}</td>
                                        <td>
                                            {% if user_stats.students > 0 %}
                                                {{ "%.1f"|format(user_stats.student_records / user_stats.students) }}
                                            {% else %}
                                                0
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <span class="badge bg-info">教师</span>
                                        </td>
                                        <td>{{ user_stats.teachers }}</td>
                                        <td>{{ user_stats.active_teachers }}</td>
                                        <td>{{ user_stats.teacher_records }}</td>
                                        <td>
                                            {% if user_stats.teachers > 0 %}
                                                {{ "%.1f"|format(user_stats.teacher_records / user_stats.teachers) }}
                                            {% else %}
                                                0
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <span class="badge bg-danger">管理员</span>
                                        </td>
                                        <td>{{ user_stats.admins }}</td>
                                        <td>{{ user_stats.active_admins }}</td>
                                        <td>{{ user_stats.admin_records }}</td>
                                        <td>
                                            {% if user_stats.admins > 0 %}
                                                {{ "%.1f"|format(user_stats.admin_records / user_stats.admins) }}
                                            {% else %}
                                                0
                                            {% endif %}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 预警统计 -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bell text-warning"></i> 预警统计
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-12 mb-3">
                                <h3 class="text-danger">{{ alert_stats.high }}</h3>
                                <small class="text-muted">高级预警</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-warning">{{ alert_stats.medium }}</h4>
                                <small class="text-muted">中级预警</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-info">{{ alert_stats.low }}</h4>
                                <small class="text-muted">低级预警</small>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row text-center">
                            <div class="col-4">
                                <h5 class="text-danger">{{ alert_stats.pending }}</h5>
                                <small class="text-muted">待处理</small>
                            </div>
                            <div class="col-4">
                                <h5 class="text-warning">{{ alert_stats.processing }}</h5>
                                <small class="text-muted">处理中</small>
                            </div>
                            <div class="col-4">
                                <h5 class="text-success">{{ alert_stats.resolved }}</h5>
                                <small class="text-muted">已解决</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统健康度 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-heart text-danger"></i> 系统健康度
                        </h5>
                    </div>
                    <div class="card-body">
                        {% set health_score = ((user_stats.active_students / user_stats.students * 100) if user_stats.students > 0 else 0) %}
                        <div class="text-center mb-3">
                            <h2 class="
                                {% if health_score >= 80 %}text-success
                                {% elif health_score >= 60 %}text-warning
                                {% else %}text-danger{% endif %}
                            ">{{ "%.0f"|format(health_score) }}%</h2>
                            <p class="text-muted">整体健康度</p>
                        </div>
                        
                        <div class="progress mb-3" style="height: 10px;">
                            <div class="progress-bar 
                                {% if health_score >= 80 %}bg-success
                                {% elif health_score >= 60 %}bg-warning
                                {% else %}bg-danger{% endif %}
                            " style="width: {{ health_score }}%"></div>
                        </div>
                        
                        <small class="text-muted">
                            基于用户活跃度、情绪健康度等指标综合评估
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 导出功能 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-download text-primary"></i> 数据导出
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">导出系统数据用于进一步分析或备份</p>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="exportData('users')">
                                <i class="fas fa-users"></i> 导出用户数据
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="exportData('emotions')">
                                <i class="fas fa-brain"></i> 导出情绪数据
                            </button>
                            <button type="button" class="btn btn-outline-warning" onclick="exportData('alerts')">
                                <i class="fas fa-exclamation-triangle"></i> 导出预警数据
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="exportData('report')">
                                <i class="fas fa-file-pdf"></i> 导出完整报告
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    // 用户活跃度图表
    var activityCtx = document.getElementById('userActivityChart').getContext('2d');
    
    // 模拟数据
    var dates = [];
    var activeUsers = [];
    
    for (var i = {{ days - 1 }}; i >= 0; i--) {
        var date = new Date();
        date.setDate(date.getDate() - i);
        dates.push(date.toLocaleDateString());
        activeUsers.push(Math.floor(Math.random() * 20) + 5);
    }
    
    new Chart(activityCtx, {
        type: 'line',
        data: {
            labels: dates,
            datasets: [{
                label: '活跃用户数',
                data: activeUsers,
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // 情绪分布图表
    var emotionCtx = document.getElementById('emotionDistributionChart').getContext('2d');
    
    var emotionData = {
        '高兴': Math.floor(Math.random() * 100) + 50,
        '悲伤': Math.floor(Math.random() * 50) + 20,
        '愤怒': Math.floor(Math.random() * 30) + 10,
        '恐惧': Math.floor(Math.random() * 25) + 5,
        '厌恶': Math.floor(Math.random() * 20) + 5,
        '喜好': Math.floor(Math.random() * 80) + 30,
        '惊讶': Math.floor(Math.random() * 40) + 15
    };
    
    new Chart(emotionCtx, {
        type: 'doughnut',
        data: {
            labels: Object.keys(emotionData),
            datasets: [{
                data: Object.values(emotionData),
                backgroundColor: [
                    '#28a745', '#dc3545', '#fd7e14', 
                    '#6f42c1', '#e83e8c', '#20c997', '#ffc107'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});

function changeTimeRange(days) {
    window.location.href = '{{ url_for("admin.reports") }}?days=' + days;
}

function exportData(type) {
    var url = '/admin/export/' + type + '?days={{ days }}';
    window.open(url, '_blank');
}
</script>
{% endblock %}
