#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
情绪相关模型
"""

from datetime import datetime
from app import db

class EmotionRecord(db.Model):
    """情绪记录模型"""
    __tablename__ = 'emotion_records'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>('users.id'), nullable=False, index=True)
    
    # 原始文本内容
    text_content = db.Column(db.Text, nullable=False)
    
    # 数据来源类型：diary(日记), chat(聊天), survey(问卷), manual(手动输入)
    source_type = db.Column(db.Enum('diary', 'chat', 'survey', 'manual'), nullable=False, default='manual')
    
    # 情绪分析结果
    predicted_emotion = db.Column(db.String(20), nullable=False)  # 预测的主要情绪
    confidence_score = db.Column(db.Float, nullable=False)  # 置信度分数
    
    # 是否为负面情绪
    is_negative = db.Column(db.<PERSON>olean, default=False, nullable=False)
    
    # 是否已处理（用于预警系统）
    is_processed = db.Column(db.Boolean, default=False, nullable=False)
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # 关系
    emotion_details = db.relationship('EmotionDetail', backref='emotion_record', lazy='dynamic', cascade='all, delete-orphan')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'text_content': self.text_content,
            'source_type': self.source_type,
            'predicted_emotion': self.predicted_emotion,
            'confidence_score': self.confidence_score,
            'is_negative': self.is_negative,
            'is_processed': self.is_processed,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'user_name': self.user.real_name if self.user else None
        }
    
    def __repr__(self):
        return f'<EmotionRecord {self.id}: {self.predicted_emotion}>'

class EmotionDetail(db.Model):
    """情绪详细分析结果模型"""
    __tablename__ = 'emotion_details'
    
    id = db.Column(db.Integer, primary_key=True)
    emotion_record_id = db.Column(db.Integer, db.ForeignKey('emotion_records.id'), nullable=False, index=True)
    
    # 具体情绪类别和概率
    emotion_type = db.Column(db.String(20), nullable=False)  # 恐惧、愤怒、厌恶、喜好、悲伤、高兴、惊讶
    probability = db.Column(db.Float, nullable=False)  # 该情绪的概率
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'emotion_type': self.emotion_type,
            'probability': self.probability
        }
    
    def __repr__(self):
        return f'<EmotionDetail {self.emotion_type}: {self.probability}>'

class EmotionAlert(db.Model):
    """情绪预警记录模型"""
    __tablename__ = 'emotion_alerts'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    emotion_record_id = db.Column(db.Integer, db.ForeignKey('emotion_records.id'), nullable=False, index=True)
    
    # 预警类型：negative(负面情绪), continuous(持续负面), severe(严重负面)
    alert_type = db.Column(db.Enum('negative', 'continuous', 'severe'), nullable=False)
    
    # 预警级别：low(低), medium(中), high(高)
    alert_level = db.Column(db.Enum('low', 'medium', 'high'), nullable=False, default='medium')
    
    # 预警描述
    alert_message = db.Column(db.Text, nullable=False)
    
    # 处理状态：pending(待处理), processing(处理中), resolved(已解决)
    status = db.Column(db.Enum('pending', 'processing', 'resolved'), nullable=False, default='pending')
    
    # 处理人员（教师或管理员）
    handler_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    
    # 处理备注
    handler_note = db.Column(db.Text, nullable=True)
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    resolved_at = db.Column(db.DateTime, nullable=True)
    
    # 关系
    handler = db.relationship('User', foreign_keys=[handler_id], backref='handled_alerts')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'emotion_record_id': self.emotion_record_id,
            'alert_type': self.alert_type,
            'alert_level': self.alert_level,
            'alert_message': self.alert_message,
            'status': self.status,
            'handler_id': self.handler_id,
            'handler_note': self.handler_note,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'resolved_at': self.resolved_at.isoformat() if self.resolved_at else None,
            'user_name': self.user.real_name if self.user else None,
            'handler_name': self.handler.real_name if self.handler else None
        }
    
    def __repr__(self):
        return f'<EmotionAlert {self.id}: {self.alert_type}>'
