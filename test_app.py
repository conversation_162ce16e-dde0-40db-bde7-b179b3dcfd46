#!/usr/bin/env python3
"""
简化版应用启动文件 - 用于测试基本功能
"""

from flask import Flask, render_template, request, jsonify
import os

def create_simple_app():
    """创建简化版应用"""
    app = Flask(__name__, template_folder='app/templates', static_folder='app/static')
    app.config['SECRET_KEY'] = 'test-secret-key'

    @app.route('/')
    def index():
        return render_template('index.html')

    @app.route('/dashboard')
    def dashboard():
        return render_template('dashboard.html')

    @app.route('/analyze')
    def analyze():
        return render_template('analyze.html')

    @app.route('/history')
    def history():
        return render_template('history.html')

    @app.route('/emotion/statistics')
    def statistics():
        return render_template('emotion/statistics.html')

    @app.route('/admin')
    def admin_index():
        return render_template('admin/index.html')

    @app.route('/admin/users')
    def admin_users():
        return render_template('admin/users.html')

    @app.route('/admin/alerts')
    def admin_alerts():
        return render_template('admin/alerts.html')

    @app.route('/admin/reports')
    def admin_reports():
        return render_template('admin/reports.html')

    @app.route('/auth/profile')
    def profile():
        return render_template('auth/profile.html')

    @app.route('/auth/edit_profile')
    def edit_profile():
        return render_template('auth/edit_profile.html')

    @app.route('/test/emotion', methods=['POST'])
    def test_emotion():
        """测试情绪分析功能"""
        try:
            data = request.get_json()
            text = data.get('text', '')

            if not text:
                return jsonify({'error': '文本不能为空'}), 400

            # 模拟情绪分析结果
            import random
            emotions = ['高兴', '悲伤', '愤怒', '恐惧', '厌恶', '喜好', '惊讶']
            predicted_emotion = random.choice(emotions)
            confidence = random.uniform(0.6, 0.95)

            result = {
                'predicted_emotion': predicted_emotion,
                'confidence': confidence,
                'is_negative': predicted_emotion in ['悲伤', '愤怒', '恐惧', '厌恶'],
                'emotion_scores': {emotion: random.uniform(0.1, 0.9) for emotion in emotions}
            }

            return jsonify(result)

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    return app

if __name__ == '__main__':
    print("启动简化版应用进行模板测试...")

    try:
        app = create_simple_app()
        print("✓ 应用创建成功！")
        print("✓ 访问 http://127.0.0.1:5000 来测试应用")
        print("✓ 可以测试所有模板页面的显示效果")
        print("✓ 按 Ctrl+C 停止服务器")

        app.run(host='127.0.0.1', port=5000, debug=True)

    except Exception as e:
        print(f"✗ 应用启动失败: {e}")
        import traceback
        traceback.print_exc()
