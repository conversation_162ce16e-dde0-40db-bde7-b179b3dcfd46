#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主页面视图
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from app import db
from app.models.user import User
from app.models.emotion import EmotionRecord, EmotionDetail, EmotionAlert
from app.utils.emotion_analyzer import emotion_analyzer
from datetime import datetime, timedelta
import logging

bp = Blueprint('main', __name__)

@bp.route('/')
def index():
    """首页"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    return render_template('index.html')

@bp.route('/dashboard')
@login_required
def dashboard():
    """用户仪表板"""
    try:
        # 获取用户最近的情绪记录
        recent_records = EmotionRecord.query.filter_by(user_id=current_user.id)\
            .order_by(EmotionRecord.created_at.desc())\
            .limit(10).all()
        
        # 获取最近7天的情绪统计
        week_ago = datetime.utcnow() - timedelta(days=7)
        week_records = EmotionRecord.query.filter(
            EmotionRecord.user_id == current_user.id,
            EmotionRecord.created_at >= week_ago
        ).all()
        
        # 统计情绪分布
        emotion_stats = {}
        for record in week_records:
            emotion = record.predicted_emotion
            emotion_stats[emotion] = emotion_stats.get(emotion, 0) + 1
        
        # 获取未处理的预警
        alerts = EmotionAlert.query.filter_by(
            user_id=current_user.id,
            status='pending'
        ).order_by(EmotionAlert.created_at.desc()).limit(5).all()
        
        return render_template('dashboard.html',
                             recent_records=recent_records,
                             emotion_stats=emotion_stats,
                             alerts=alerts,
                             total_records=len(week_records))
    
    except Exception as e:
        logging.error(f"仪表板加载失败: {str(e)}")
        flash('仪表板加载失败，请稍后重试', 'error')
        return render_template('dashboard.html',
                             recent_records=[],
                             emotion_stats={},
                             alerts=[],
                             total_records=0)

@bp.route('/analyze', methods=['GET', 'POST'])
@login_required
def analyze():
    """情绪分析页面"""
    if request.method == 'POST':
        try:
            text = request.form.get('text', '').strip()
            source_type = request.form.get('source_type', 'manual')
            
            if not text:
                flash('请输入要分析的文本内容', 'error')
                return render_template('analyze.html')
            
            # 确保模型已加载
            if not emotion_analyzer.model:
                if not emotion_analyzer.load_model():
                    flash('情绪分析模型加载失败，请联系管理员', 'error')
                    return render_template('analyze.html')
            
            # 进行情绪分析
            result = emotion_analyzer.predict(text)
            
            # 保存分析结果到数据库
            emotion_record = EmotionRecord(
                user_id=current_user.id,
                text_content=text,
                source_type=source_type,
                predicted_emotion=result['predicted_emotion'],
                confidence_score=result['confidence'],
                is_negative=result['is_negative']
            )
            db.session.add(emotion_record)
            db.session.flush()  # 获取记录ID
            
            # 保存详细情绪分数
            for emotion_type, probability in result['emotion_scores'].items():
                emotion_detail = EmotionDetail(
                    emotion_record_id=emotion_record.id,
                    emotion_type=emotion_type,
                    probability=probability
                )
                db.session.add(emotion_detail)
            
            db.session.commit()
            
            # 检查是否需要预警
            if result['is_negative'] and result['confidence'] > 0.7:
                alert = EmotionAlert(
                    user_id=current_user.id,
                    emotion_record_id=emotion_record.id,
                    alert_type='negative',
                    alert_level='medium',
                    alert_message=f"检测到负面情绪：{result['predicted_emotion']}，置信度：{result['confidence']:.2f}"
                )
                db.session.add(alert)
                db.session.commit()
            
            flash(f'情绪分析完成！检测到情绪：{result["predicted_emotion"]}', 'success')
            return render_template('analyze.html', result=result)
            
        except Exception as e:
            logging.error(f"情绪分析失败: {str(e)}")
            flash('情绪分析失败，请稍后重试', 'error')
            db.session.rollback()
    
    return render_template('analyze.html')

@bp.route('/api/emotion/analyze', methods=['POST'])
@login_required
def api_analyze():
    """情绪分析API接口"""
    try:
        data = request.get_json()
        if not data or 'text' not in data:
            return jsonify({'error': '缺少文本参数'}), 400
        
        text = data['text'].strip()
        if not text:
            return jsonify({'error': '文本内容不能为空'}), 400
        
        # 确保模型已加载
        if not emotion_analyzer.model:
            if not emotion_analyzer.load_model():
                return jsonify({'error': '模型加载失败'}), 500
        
        # 进行情绪分析
        result = emotion_analyzer.predict(text)
        
        # 可选：保存到数据库
        if data.get('save', False):
            emotion_record = EmotionRecord(
                user_id=current_user.id,
                text_content=text,
                source_type=data.get('source_type', 'api'),
                predicted_emotion=result['predicted_emotion'],
                confidence_score=result['confidence'],
                is_negative=result['is_negative']
            )
            db.session.add(emotion_record)
            db.session.commit()
            result['record_id'] = emotion_record.id
        
        return jsonify(result)
        
    except Exception as e:
        logging.error(f"API情绪分析失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@bp.route('/history')
@login_required
def history():
    """情绪记录历史"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20
        
        records = EmotionRecord.query.filter_by(user_id=current_user.id)\
            .order_by(EmotionRecord.created_at.desc())\
            .paginate(page=page, per_page=per_page, error_out=False)
        
        return render_template('history.html', records=records)
        
    except Exception as e:
        logging.error(f"历史记录加载失败: {str(e)}")
        flash('历史记录加载失败，请稍后重试', 'error')
        return render_template('history.html', records=None)
