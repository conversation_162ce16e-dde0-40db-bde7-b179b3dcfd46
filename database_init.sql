-- 黔南民族中学学生情绪分析系统数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS emotion_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE emotion_analysis;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(80) NOT NULL UNIQUE,
    email VARCHAR(120) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    real_name VARCHAR(50) NOT NULL,
    user_type ENUM('student', 'teacher', 'admin') NOT NULL DEFAULT 'student',
    student_id VARCHAR(20) UNIQUE,
    class_name VARCHAR(50),
    grade VARCHAR(20),
    teacher_id VARCHAR(20) UNIQUE,
    subject VARCHAR(50),
    phone VARCHAR(20),
    avatar VARCHAR(200),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_student_id (student_id),
    INDEX idx_teacher_id (teacher_id)
);

-- 创建情绪记录表
CREATE TABLE IF NOT EXISTS emotion_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    text_content TEXT NOT NULL,
    source_type ENUM('diary', 'chat', 'survey', 'manual') NOT NULL DEFAULT 'manual',
    predicted_emotion VARCHAR(20) NOT NULL,
    confidence_score FLOAT NOT NULL,
    is_negative BOOLEAN NOT NULL DEFAULT FALSE,
    is_processed BOOLEAN NOT NULL DEFAULT FALSE,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_predicted_emotion (predicted_emotion)
);

-- 创建情绪详细分析表
CREATE TABLE IF NOT EXISTS emotion_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    emotion_record_id INT NOT NULL,
    emotion_type VARCHAR(20) NOT NULL,
    probability FLOAT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (emotion_record_id) REFERENCES emotion_records(id) ON DELETE CASCADE,
    INDEX idx_emotion_record_id (emotion_record_id)
);

-- 创建情绪预警表
CREATE TABLE IF NOT EXISTS emotion_alerts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    emotion_record_id INT NOT NULL,
    alert_type ENUM('negative', 'continuous', 'severe') NOT NULL,
    alert_level ENUM('low', 'medium', 'high') NOT NULL DEFAULT 'medium',
    alert_message TEXT NOT NULL,
    status ENUM('pending', 'processing', 'resolved') NOT NULL DEFAULT 'pending',
    handler_id INT,
    handler_note TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    resolved_at DATETIME,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (emotion_record_id) REFERENCES emotion_records(id) ON DELETE CASCADE,
    FOREIGN KEY (handler_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_status (status)
);

-- 插入默认管理员用户
INSERT INTO users (username, email, password_hash, real_name, user_type) 
VALUES ('admin', '<EMAIL>', 'pbkdf2:sha256:260000$salt$hash', '系统管理员', 'admin')
ON DUPLICATE KEY UPDATE username=username;

-- 插入示例教师用户
INSERT INTO users (username, email, password_hash, real_name, user_type, teacher_id, subject) 
VALUES ('teacher001', '<EMAIL>', 'pbkdf2:sha256:260000$salt$hash', '张老师', 'teacher', 'T001', '语文')
ON DUPLICATE KEY UPDATE username=username;

-- 插入示例学生用户
INSERT INTO users (username, email, password_hash, real_name, user_type, student_id, class_name, grade) 
VALUES ('student001', '<EMAIL>', 'pbkdf2:sha256:260000$salt$hash', '李小明', 'student', '********', '高三(1)班', '高三')
ON DUPLICATE KEY UPDATE username=username;
