#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask应用工厂函数
"""

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
import os

# 初始化扩展
db = SQLAlchemy()
login_manager = LoginManager()

def create_app(config_class):
    """应用工厂函数"""
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # 初始化扩展
    db.init_app(app)
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = '请先登录以访问此页面。'
    login_manager.login_message_category = 'info'
    
    # 注册蓝图
    from app.views.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    from app.views.main import bp as main_bp
    app.register_blueprint(main_bp)
    
    from app.views.emotion import bp as emotion_bp
    app.register_blueprint(emotion_bp, url_prefix='/emotion')
    
    from app.views.admin import bp as admin_bp
    app.register_blueprint(admin_bp, url_prefix='/admin')
    
    # 创建上传目录
    upload_folder = app.config.get('UPLOAD_FOLDER')
    if upload_folder and not os.path.exists(upload_folder):
        os.makedirs(upload_folder)
    
    # 创建数据库表
    with app.app_context():
        db.create_all()
    
    return app

@login_manager.user_loader
def load_user(user_id):
    """用户加载回调函数"""
    from app.models.user import User
    return User.query.get(int(user_id))
