# 黔南民族中学学生情绪分析系统

基于StructBERT深度学习模型的中文情绪分析系统，为学生心理健康提供智能化分析与预警服务。

## 系统特色

- 🧠 **智能情绪识别**: 基于阿里巴巴StructBERT模型，准确识别七种情绪类型
- 📊 **数据可视化**: 直观的图表展示情绪趋势和统计分析
- ⚠️ **智能预警**: 自动检测负面情绪，及时预警心理健康问题
- 👥 **多角色管理**: 支持学生、教师、管理员分级权限管理
- 📱 **响应式设计**: 支持PC端和移动端访问

## 技术栈

- **后端**: Python 3.8+, Flask 2.3+
- **数据库**: MySQL 8.0+
- **AI模型**: StructBERT (nlp_structbert_emotion-classification_chinese-base)
- **前端**: Bootstrap 5, Chart.js, jQuery
- **部署**: Gun<PERSON>, Nginx (可选)

## 情绪分类

系统可识别以下七种情绪类型：
- 😊 高兴 (Happy)
- 😢 悲伤 (Sad)  
- 😠 愤怒 (Angry)
- 😨 恐惧 (Fear)
- 🤢 厌恶 (Disgust)
- 😍 喜好 (Like)
- 😲 惊讶 (Surprise)

## 安装部署

### 1. 环境要求

- Python 3.8 或更高版本
- MySQL 8.0 或更高版本
- 至少 4GB 内存 (推荐 8GB)

### 2. 克隆项目

```bash
git clone <repository-url>
cd emotion-analysis-system
```

### 3. 安装依赖

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 4. 数据库配置

```bash
# 创建数据库
mysql -u root -p < database_init.sql

# 或手动创建数据库
mysql -u root -p
CREATE DATABASE emotion_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 5. 环境配置

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
# 设置数据库连接信息、密钥等
```

### 6. 启动应用

```bash
# 开发模式
python app.py

# 生产模式
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

访问 http://localhost:5000 即可使用系统。

## 默认账户

系统初始化后会创建以下默认账户：

- **管理员**: admin / admin123
- **教师**: teacher001 / teacher123  
- **学生**: student001 / student123

⚠️ **重要**: 首次登录后请立即修改默认密码！

## 功能模块

### 学生功能
- 情绪文本输入与分析
- 个人情绪历史记录
- 情绪趋势统计图表
- 个人资料管理

### 教师功能
- 查看学生情绪分析结果
- 情绪预警通知处理
- 班级情绪统计报告
- 学生心理健康监控

### 管理员功能
- 用户账户管理
- 系统预警管理
- 全校情绪统计报告
- 系统配置管理

## API接口

### 情绪分析API

```bash
POST /api/emotion/analyze
Content-Type: application/json

{
    "text": "今天心情很好，阳光明媚！",
    "save": true,
    "source_type": "manual"
}
```

### 批量分析API

```bash
POST /emotion/api/batch_analyze
Content-Type: application/json

{
    "texts": ["文本1", "文本2", "文本3"],
    "save": true,
    "source_type": "batch"
}
```

## 目录结构

```
emotion-analysis-system/
├── app/                          # 应用主目录
│   ├── models/                   # 数据模型
│   │   ├── user.py              # 用户模型
│   │   └── emotion.py           # 情绪模型
│   ├── views/                    # 视图控制器
│   │   ├── auth.py              # 认证视图
│   │   ├── main.py              # 主页视图
│   │   ├── emotion.py           # 情绪分析视图
│   │   └── admin.py             # 管理员视图
│   ├── templates/                # HTML模板
│   ├── static/                   # 静态文件
│   │   ├── css/                 # 样式文件
│   │   ├── js/                  # JavaScript文件
│   │   └── images/              # 图片文件
│   ├── utils/                    # 工具类
│   │   └── emotion_analyzer.py  # 情绪分析器
│   └── __init__.py              # 应用初始化
├── nlp_structbert_emotion-classification_chinese-base/  # AI模型文件
├── migrations/                   # 数据库迁移文件
├── app.py                       # 应用入口
├── config.py                    # 配置文件
├── requirements.txt             # 依赖列表
├── database_init.sql            # 数据库初始化脚本
└── README.md                    # 项目说明
```

## 开发指南

### 添加新的情绪类型

1. 修改 `app/utils/emotion_analyzer.py` 中的 `emotion_labels` 字典
2. 更新数据库模型和前端显示逻辑
3. 重新训练或更新AI模型

### 自定义预警规则

在 `config.py` 中修改以下配置：

```python
NEGATIVE_EMOTION_THRESHOLD = 0.7  # 负面情绪预警阈值
ALERT_EMOTIONS = ['恐惧', '愤怒', '厌恶', '悲伤']  # 需要预警的情绪类型
```

### 扩展数据源

系统支持多种数据来源：
- `manual`: 手动输入
- `diary`: 日记记录
- `chat`: 聊天对话
- `survey`: 问卷调查

可在 `app/models/emotion.py` 中添加新的数据源类型。

## 常见问题

### Q: 模型加载失败怎么办？
A: 确保 `nlp_structbert_emotion-classification_chinese-base` 目录存在且包含完整的模型文件。

### Q: 数据库连接失败？
A: 检查 `.env` 文件中的数据库配置信息是否正确。

### Q: 情绪分析准确率如何提升？
A: 可以使用更多的训练数据对模型进行微调，或者调整预测阈值。

## 贡献指南

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目作者: [您的姓名]
- 邮箱: [您的邮箱]
- 项目链接: [项目地址]

## 致谢

- 感谢阿里巴巴提供的StructBERT预训练模型
- 感谢所有为本项目做出贡献的开发者
