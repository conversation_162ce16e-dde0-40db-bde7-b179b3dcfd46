{% extends "base.html" %}

{% block title %}编辑资料 - 黔南民族中学学生情绪分析系统{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit text-primary"></i> 编辑个人资料
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="editProfileForm">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.real_name.label(class="form-label") }}
                                {{ form.real_name(class="form-control") }}
                                {% if form.real_name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.real_name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.email.label(class="form-label") }}
                                {{ form.email(class="form-control") }}
                                {% if form.email.errors %}
                                    <div class="text-danger">
                                        {% for error in form.email.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    {% if current_user.is_student() %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.student_id.label(class="form-label") }}
                                {{ form.student_id(class="form-control") }}
                                {% if form.student_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.student_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.class_name.label(class="form-label") }}
                                {{ form.class_name(class="form-control") }}
                                {% if form.class_name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.class_name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="mb-3">
                        <label class="form-label">用户名</label>
                        <input type="text" class="form-control" value="{{ current_user.username }}" disabled>
                        <small class="text-muted">用户名不可修改</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">角色</label>
                        <input type="text" class="form-control" 
                               value="{% if current_user.is_admin() %}管理员{% elif current_user.is_teacher() %}教师{% else %}学生{% endif %}" 
                               disabled>
                        <small class="text-muted">角色不可修改</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">注册时间</label>
                        <input type="text" class="form-control" 
                               value="{{ current_user.created_at.strftime('%Y-%m-%d %H:%M:%S') }}" 
                               disabled>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('auth.profile') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 保存修改
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 修改密码卡片 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-key text-warning"></i> 修改密码
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('auth.change_password') }}" id="changePasswordForm">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">当前密码</label>
                        <input type="password" class="form-control" id="current_password" 
                               name="current_password" required>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="new_password" class="form-label">新密码</label>
                                <input type="password" class="form-control" id="new_password" 
                                       name="new_password" required minlength="6">
                                <small class="text-muted">密码长度至少6位</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">确认新密码</label>
                                <input type="password" class="form-control" id="confirm_password" 
                                       name="confirm_password" required>
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-key"></i> 修改密码
                    </button>
                </form>
            </div>
        </div>

        <!-- 账户安全 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shield-alt text-info"></i> 账户安全
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>登录记录</h6>
                        <p class="text-muted">
                            最后登录时间：
                            {% if current_user.last_login %}
                                {{ current_user.last_login.strftime('%Y-%m-%d %H:%M:%S') }}
                            {% else %}
                                从未登录
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6>账户状态</h6>
                        <p>
                            {% if current_user.is_active %}
                                <span class="badge bg-success">正常</span>
                            {% else %}
                                <span class="badge bg-danger">禁用</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>安全提示：</strong>
                    <ul class="mb-0 mt-2">
                        <li>定期修改密码，使用强密码</li>
                        <li>不要在公共场所登录账户</li>
                        <li>发现异常登录请及时联系管理员</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    // 编辑资料表单提交
    $('#editProfileForm').on('submit', function(e) {
        e.preventDefault();
        
        var formData = new FormData(this);
        
        $.ajax({
            url: '{{ url_for("auth.edit_profile") }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    alert('资料更新成功！');
                    window.location.href = '{{ url_for("auth.profile") }}';
                } else {
                    alert('更新失败：' + (response.message || '未知错误'));
                }
            },
            error: function(xhr) {
                var error = xhr.responseJSON ? xhr.responseJSON.message : '更新失败';
                alert(error);
            }
        });
    });
    
    // 修改密码表单提交
    $('#changePasswordForm').on('submit', function(e) {
        e.preventDefault();
        
        var newPassword = $('#new_password').val();
        var confirmPassword = $('#confirm_password').val();
        
        if (newPassword !== confirmPassword) {
            alert('两次输入的密码不一致！');
            return;
        }
        
        if (newPassword.length < 6) {
            alert('密码长度至少6位！');
            return;
        }
        
        var formData = new FormData(this);
        
        $.ajax({
            url: '{{ url_for("auth.change_password") }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    alert('密码修改成功！');
                    $('#changePasswordForm')[0].reset();
                } else {
                    alert('修改失败：' + (response.message || '未知错误'));
                }
            },
            error: function(xhr) {
                var error = xhr.responseJSON ? xhr.responseJSON.message : '修改失败';
                alert(error);
            }
        });
    });
    
    // 密码强度检查
    $('#new_password').on('input', function() {
        var password = $(this).val();
        var strength = 0;
        
        if (password.length >= 6) strength++;
        if (password.match(/[a-z]/)) strength++;
        if (password.match(/[A-Z]/)) strength++;
        if (password.match(/[0-9]/)) strength++;
        if (password.match(/[^a-zA-Z0-9]/)) strength++;
        
        var strengthText = '';
        var strengthClass = '';
        
        switch(strength) {
            case 0:
            case 1:
                strengthText = '弱';
                strengthClass = 'text-danger';
                break;
            case 2:
            case 3:
                strengthText = '中等';
                strengthClass = 'text-warning';
                break;
            case 4:
            case 5:
                strengthText = '强';
                strengthClass = 'text-success';
                break;
        }
        
        var strengthIndicator = $(this).siblings('.password-strength');
        if (strengthIndicator.length === 0) {
            $(this).after('<small class="password-strength"></small>');
            strengthIndicator = $(this).siblings('.password-strength');
        }
        
        if (password.length > 0) {
            strengthIndicator.html('密码强度: <span class="' + strengthClass + '">' + strengthText + '</span>');
        } else {
            strengthIndicator.html('');
        }
    });
});
</script>
{% endblock %}
