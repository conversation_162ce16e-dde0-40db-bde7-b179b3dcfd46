{% extends "base.html" %}

{% block title %}情绪分析 - 黔南民族中学学生情绪分析系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-brain text-primary"></i> 情绪分析
                </h4>
            </div>
            <div class="card-body">
                <form id="emotion-form" method="POST">
                    <div class="mb-3">
                        <label for="text" class="form-label">请输入要分析的文本内容</label>
                        <textarea class="form-control" id="text" name="text" rows="6" 
                                placeholder="请输入您的心情、日记、想法或任何文本内容..." required></textarea>
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i> 
                            系统将分析您的文本并识别其中的情绪类型（高兴、悲伤、愤怒、恐惧、厌恶、喜好、惊讶）
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="source_type" class="form-label">数据来源</label>
                        <select class="form-select" id="source_type" name="source_type">
                            <option value="manual">手动输入</option>
                            <option value="diary">日记记录</option>
                            <option value="chat">聊天对话</option>
                            <option value="survey">问卷调查</option>
                        </select>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-brain"></i> 开始分析
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 分析结果显示区域 -->
        <div id="analysis-result" style="display: none;">
            {% if result %}
            <div class="emotion-result mt-4">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h4>分析结果</h4>
                        <div class="emotion-score">{{ result.predicted_emotion }}</div>
                        <p class="mb-2">置信度: {{ "%.1f"|format(result.confidence * 100) }}%</p>
                        <div class="confidence-bar">
                            <div class="confidence-fill" style="width: {{ result.confidence * 100 }}%"></div>
                        </div>
                        <div class="mt-3">
                            {% if result.is_negative %}
                                <span class="badge bg-danger">负面情绪</span>
                            {% elif result.is_positive %}
                                <span class="badge bg-success">正面情绪</span>
                            {% else %}
                                <span class="badge bg-secondary">中性情绪</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="emotion-details">
                            <h6>详细分数</h6>
                            {% for emotion, score in result.emotion_scores.items() %}
                            <div class="emotion-item">
                                <span>{{ emotion }}</span>
                                <div class="d-flex align-items-center">
                                    <span class="me-2">{{ "%.1f"|format(score * 100) }}%</span>
                                    <div class="emotion-bar">
                                        <div class="emotion-bar-fill bg-primary" style="width: {{ score * 100 }}%"></div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- 使用说明 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-question-circle text-info"></i> 使用说明
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>情绪类型说明</h6>
                        <ul class="list-unstyled">
                            <li><span class="emotion-label emotion-happy">高兴</span> - 积极、愉快的情绪</li>
                            <li><span class="emotion-label emotion-like">喜好</span> - 喜爱、偏好的情绪</li>
                            <li><span class="emotion-label emotion-surprise">惊讶</span> - 意外、惊奇的情绪</li>
                            <li><span class="emotion-label emotion-sad">悲伤</span> - 难过、沮丧的情绪</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>负面情绪类型</h6>
                        <ul class="list-unstyled">
                            <li><span class="emotion-label emotion-angry">愤怒</span> - 生气、愤慨的情绪</li>
                            <li><span class="emotion-label emotion-fear">恐惧</span> - 害怕、担心的情绪</li>
                            <li><span class="emotion-label emotion-disgust">厌恶</span> - 反感、厌烦的情绪</li>
                        </ul>
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>温馨提示:</strong> 如果检测到负面情绪且置信度较高，系统会自动生成预警通知相关教师。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    // 如果有分析结果，显示结果区域
    {% if result %}
    $('#analysis-result').show();
    {% endif %}
    
    // 文本输入计数
    $('#text').on('input', function() {
        var length = $(this).val().length;
        var maxLength = 500;
        
        if (length > maxLength) {
            $(this).val($(this).val().substring(0, maxLength));
            length = maxLength;
        }
        
        // 可以添加字符计数显示
        // $('.char-count').text(length + '/' + maxLength);
    });
});
</script>
{% endblock %}
