{% extends "base.html" %}

{% block title %}预警管理 - 黔南民族中学学生情绪分析系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-exclamation-triangle text-warning"></i> 
                预警管理
            </h2>
            <div class="btn-group" role="group">
                <a href="?status=pending" class="btn btn-outline-danger {{ 'active' if request.args.get('status') == 'pending' else '' }}">
                    待处理
                </a>
                <a href="?status=processing" class="btn btn-outline-warning {{ 'active' if request.args.get('status') == 'processing' else '' }}">
                    处理中
                </a>
                <a href="?status=resolved" class="btn btn-outline-success {{ 'active' if request.args.get('status') == 'resolved' else '' }}">
                    已解决
                </a>
                <a href="?" class="btn btn-outline-secondary {{ 'active' if not request.args.get('status') else '' }}">
                    全部
                </a>
            </div>
        </div>

        <!-- 预警统计 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center border-danger">
                    <div class="card-body">
                        <i class="fas fa-clock fa-2x text-danger mb-2"></i>
                        <h4 class="card-title text-danger">
                            {{ alerts.items|selectattr('status', 'equalto', 'pending')|list|length if alerts else 0 }}
                        </h4>
                        <p class="card-text">待处理</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-warning">
                    <div class="card-body">
                        <i class="fas fa-cog fa-2x text-warning mb-2"></i>
                        <h4 class="card-title text-warning">
                            {{ alerts.items|selectattr('status', 'equalto', 'processing')|list|length if alerts else 0 }}
                        </h4>
                        <p class="card-text">处理中</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-success">
                    <div class="card-body">
                        <i class="fas fa-check fa-2x text-success mb-2"></i>
                        <h4 class="card-title text-success">
                            {{ alerts.items|selectattr('status', 'equalto', 'resolved')|list|length if alerts else 0 }}
                        </h4>
                        <p class="card-text">已解决</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-info">
                    <div class="card-body">
                        <i class="fas fa-list fa-2x text-info mb-2"></i>
                        <h4 class="card-title text-info">{{ alerts.total if alerts else 0 }}</h4>
                        <p class="card-text">总计</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 预警列表 -->
        {% if alerts and alerts.items %}
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>学生</th>
                                <th>预警类型</th>
                                <th>预警级别</th>
                                <th>预警信息</th>
                                <th>状态</th>
                                <th>处理人</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for alert in alerts.items %}
                            <tr class="alert-row-{{ alert.alert_level }}">
                                <td>
                                    <small class="text-muted">
                                        {{ alert.created_at.strftime('%Y-%m-%d') }}<br>
                                        {{ alert.created_at.strftime('%H:%M:%S') }}
                                    </small>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div>
                                            <strong>{{ alert.user.real_name if alert.user else '未知用户' }}</strong><br>
                                            {% if alert.user and alert.user.is_student() %}
                                                <small class="text-muted">
                                                    {{ alert.user.class_name }} - {{ alert.user.student_id }}
                                                </small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ alert.alert_type }}</span>
                                </td>
                                <td>
                                    {% if alert.alert_level == 'high' %}
                                        <span class="badge bg-danger">高</span>
                                    {% elif alert.alert_level == 'medium' %}
                                        <span class="badge bg-warning">中</span>
                                    {% else %}
                                        <span class="badge bg-info">低</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" 
                                         data-bs-toggle="tooltip" 
                                         title="{{ alert.alert_message }}">
                                        {{ alert.alert_message }}
                                    </div>
                                </td>
                                <td>
                                    {% if alert.status == 'pending' %}
                                        <span class="badge bg-danger">待处理</span>
                                    {% elif alert.status == 'processing' %}
                                        <span class="badge bg-warning">处理中</span>
                                    {% else %}
                                        <span class="badge bg-success">已解决</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if alert.handler %}
                                        <small>{{ alert.handler.real_name }}</small>
                                    {% else %}
                                        <small class="text-muted">未分配</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button class="btn btn-outline-info" 
                                                onclick="viewAlertDetail({{ alert.id }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        {% if current_user.is_teacher() or current_user.is_admin() %}
                                            {% if alert.status == 'pending' %}
                                                <button class="btn btn-outline-warning handle-alert-btn" 
                                                        data-alert-id="{{ alert.id }}" 
                                                        data-action="processing">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                            {% endif %}
                                            {% if alert.status in ['pending', 'processing'] %}
                                                <button class="btn btn-outline-success handle-alert-btn" 
                                                        data-alert-id="{{ alert.id }}" 
                                                        data-action="resolved">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            {% endif %}
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                {% if alerts.pages > 1 %}
                <nav aria-label="预警分页">
                    <ul class="pagination justify-content-center mt-4">
                        {% if alerts.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.alerts', page=alerts.prev_num, **request.args) }}">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        {% endif %}

                        {% for page_num in alerts.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != alerts.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin.alerts', page=page_num, **request.args) }}">
                                        {{ page_num }}
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if alerts.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.alerts', page=alerts.next_num, **request.args) }}">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
        {% else %}
        <!-- 空状态 -->
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-shield-alt fa-4x text-success mb-4"></i>
                <h4 class="text-muted">暂无预警</h4>
                <p class="text-muted">当前没有需要处理的情绪预警。</p>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- 预警详情模态框 -->
<div class="modal fade" id="alertDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">预警详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="alertDetailContent">
                <!-- 详情内容将通过AJAX加载 -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function viewAlertDetail(alertId) {
    $('#alertDetailContent').html('<div class="text-center"><div class="spinner-border" role="status"></div></div>');
    $('#alertDetailModal').modal('show');
    
    $.get(`/emotion/api/alert/${alertId}`)
        .done(function(data) {
            var html = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr><td>学生:</td><td>${data.user_name || '未知用户'}</td></tr>
                            <tr><td>预警时间:</td><td>${new Date(data.created_at).toLocaleString()}</td></tr>
                            <tr><td>预警类型:</td><td><span class="badge bg-info">${data.alert_type}</span></td></tr>
                            <tr><td>预警级别:</td><td>${data.alert_level}</td></tr>
                            <tr><td>状态:</td><td>${data.status}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>处理信息</h6>
                        <table class="table table-sm">
                            <tr><td>处理人:</td><td>${data.handler_name || '未分配'}</td></tr>
                            <tr><td>更新时间:</td><td>${data.updated_at ? new Date(data.updated_at).toLocaleString() : '-'}</td></tr>
                        </table>
                    </div>
                </div>
                <div class="mt-3">
                    <h6>预警信息</h6>
                    <div class="border rounded p-3 bg-light">
                        ${data.alert_message}
                    </div>
                </div>
            `;
            $('#alertDetailContent').html(html);
        })
        .fail(function() {
            $('#alertDetailContent').html('<div class="alert alert-danger">加载失败，请稍后重试</div>');
        });
}

$('.handle-alert-btn').on('click', function() {
    var alertId = $(this).data('alert-id');
    var action = $(this).data('action');
    var actionText = action === 'processing' ? '开始处理' : '标记为已解决';
    var note = prompt(`${actionText}这个预警，请输入处理备注（可选）：`);
    
    if (note === null) return;
    
    $.ajax({
        url: `/admin/alerts/${alertId}/handle`,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            action: action,
            note: note
        }),
        success: function(response) {
            alert(response.message);
            location.reload();
        },
        error: function(xhr) {
            var error = xhr.responseJSON ? xhr.responseJSON.error : '处理失败';
            alert(error);
        }
    });
});
</script>

<style>
.alert-row-high {
    border-left: 4px solid #dc3545;
}
.alert-row-medium {
    border-left: 4px solid #ffc107;
}
.alert-row-low {
    border-left: 4px solid #17a2b8;
}
</style>
{% endblock %}
