{% extends "base.html" %}

{% block title %}用户管理 - 黔南民族中学学生情绪分析系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-users text-primary"></i> 
                用户管理
            </h2>
            <div>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                    <i class="fas fa-plus"></i> 添加用户
                </button>
            </div>
        </div>

        <!-- 筛选器 -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="role_filter" class="form-label">用户角色</label>
                        <select class="form-select" id="role_filter" name="role">
                            <option value="">全部角色</option>
                            <option value="student" {% if request.args.get('role') == 'student' %}selected{% endif %}>学生</option>
                            <option value="teacher" {% if request.args.get('role') == 'teacher' %}selected{% endif %}>教师</option>
                            <option value="admin" {% if request.args.get('role') == 'admin' %}selected{% endif %}>管理员</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="status_filter" class="form-label">账户状态</label>
                        <select class="form-select" id="status_filter" name="status">
                            <option value="">全部状态</option>
                            <option value="active" {% if request.args.get('status') == 'active' %}selected{% endif %}>正常</option>
                            <option value="inactive" {% if request.args.get('status') == 'inactive' %}selected{% endif %}>禁用</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="search" class="form-label">搜索</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               placeholder="姓名、用户名或学号" value="{{ request.args.get('search', '') }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 用户列表 -->
        {% if users and users.items %}
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>用户信息</th>
                                <th>角色</th>
                                <th>注册时间</th>
                                <th>最后登录</th>
                                <th>状态</th>
                                <th>情绪记录</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users.items %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-3">
                                            {{ user.real_name[0] if user.real_name else user.username[0] }}
                                        </div>
                                        <div>
                                            <strong>{{ user.real_name or user.username }}</strong><br>
                                            <small class="text-muted">{{ user.username }}</small>
                                            {% if user.is_student() and user.student_id %}
                                                <br><small class="text-muted">学号: {{ user.student_id }}</small>
                                            {% endif %}
                                            {% if user.is_student() and user.class_name %}
                                                <br><small class="text-muted">班级: {{ user.class_name }}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if user.is_admin() %}
                                        <span class="badge bg-danger">管理员</span>
                                    {% elif user.is_teacher() %}
                                        <span class="badge bg-info">教师</span>
                                    {% else %}
                                        <span class="badge bg-success">学生</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small>{{ user.created_at.strftime('%Y-%m-%d') }}</small>
                                </td>
                                <td>
                                    {% if user.last_login %}
                                        <small>{{ user.last_login.strftime('%Y-%m-%d %H:%M') }}</small>
                                    {% else %}
                                        <small class="text-muted">从未登录</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.is_active %}
                                        <span class="badge bg-success">正常</span>
                                    {% else %}
                                        <span class="badge bg-danger">禁用</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark">
                                        {{ user.emotion_records|length }} 条
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button class="btn btn-outline-info" 
                                                onclick="viewUserDetail({{ user.id }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-primary" 
                                                onclick="editUser({{ user.id }})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        {% if user.is_active %}
                                            <button class="btn btn-outline-warning" 
                                                    onclick="toggleUserStatus({{ user.id }}, false)">
                                                <i class="fas fa-ban"></i>
                                            </button>
                                        {% else %}
                                            <button class="btn btn-outline-success" 
                                                    onclick="toggleUserStatus({{ user.id }}, true)">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        {% endif %}
                                        {% if user.id != current_user.id %}
                                            <button class="btn btn-outline-danger" 
                                                    onclick="deleteUser({{ user.id }})">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                {% if users.pages > 1 %}
                <nav aria-label="用户分页">
                    <ul class="pagination justify-content-center mt-4">
                        {% if users.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.users', page=users.prev_num, **request.args) }}">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        {% endif %}

                        {% for page_num in users.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != users.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin.users', page=page_num, **request.args) }}">
                                        {{ page_num }}
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if users.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.users', page=users.next_num, **request.args) }}">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                <!-- 统计信息 -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <small class="text-muted">
                            显示第 {{ users.per_page * (users.page - 1) + 1 }} - 
                            {{ users.per_page * (users.page - 1) + users.items|length }} 条，
                            共 {{ users.total }} 条记录
                        </small>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <!-- 空状态 -->
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-users fa-4x text-muted mb-4"></i>
                <h4 class="text-muted">暂无用户</h4>
                <p class="text-muted">当前筛选条件下没有找到相关用户。</p>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- 添加用户模态框 -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addUserForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="username" class="form-label">用户名 *</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="real_name" class="form-label">真实姓名 *</label>
                        <input type="text" class="form-control" id="real_name" name="real_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="email" name="email">
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">角色 *</label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="student">学生</option>
                            <option value="teacher">教师</option>
                            <option value="admin">管理员</option>
                        </select>
                    </div>
                    <div class="mb-3" id="studentFields" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="student_id" class="form-label">学号</label>
                                <input type="text" class="form-control" id="student_id" name="student_id">
                            </div>
                            <div class="col-md-6">
                                <label for="class_name" class="form-label">班级</label>
                                <input type="text" class="form-control" id="class_name" name="class_name">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">密码 *</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">添加</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 用户详情模态框 -->
<div class="modal fade" id="userDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">用户详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetailContent">
                <!-- 详情内容将通过AJAX加载 -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// 角色选择变化时显示/隐藏学生字段
$('#role').on('change', function() {
    if ($(this).val() === 'student') {
        $('#studentFields').show();
    } else {
        $('#studentFields').hide();
    }
});

// 添加用户表单提交
$('#addUserForm').on('submit', function(e) {
    e.preventDefault();
    
    var formData = new FormData(this);
    var data = {};
    formData.forEach((value, key) => {
        data[key] = value;
    });
    
    $.ajax({
        url: '/admin/users',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(response) {
            alert('用户添加成功');
            $('#addUserModal').modal('hide');
            location.reload();
        },
        error: function(xhr) {
            var error = xhr.responseJSON ? xhr.responseJSON.error : '添加失败';
            alert(error);
        }
    });
});

function viewUserDetail(userId) {
    $('#userDetailContent').html('<div class="text-center"><div class="spinner-border" role="status"></div></div>');
    $('#userDetailModal').modal('show');
    
    $.get(`/admin/users/${userId}`)
        .done(function(data) {
            var html = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr><td>用户名:</td><td>${data.username}</td></tr>
                            <tr><td>真实姓名:</td><td>${data.real_name || '-'}</td></tr>
                            <tr><td>邮箱:</td><td>${data.email || '-'}</td></tr>
                            <tr><td>角色:</td><td>${data.role}</td></tr>
                            ${data.student_id ? `<tr><td>学号:</td><td>${data.student_id}</td></tr>` : ''}
                            ${data.class_name ? `<tr><td>班级:</td><td>${data.class_name}</td></tr>` : ''}
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>账户信息</h6>
                        <table class="table table-sm">
                            <tr><td>注册时间:</td><td>${new Date(data.created_at).toLocaleString()}</td></tr>
                            <tr><td>最后登录:</td><td>${data.last_login ? new Date(data.last_login).toLocaleString() : '从未登录'}</td></tr>
                            <tr><td>状态:</td><td>${data.is_active ? '正常' : '禁用'}</td></tr>
                            <tr><td>情绪记录:</td><td>${data.emotion_count} 条</td></tr>
                        </table>
                    </div>
                </div>
            `;
            $('#userDetailContent').html(html);
        })
        .fail(function() {
            $('#userDetailContent').html('<div class="alert alert-danger">加载失败，请稍后重试</div>');
        });
}

function editUser(userId) {
    // 这里可以实现编辑用户功能
    alert('编辑功能待实现');
}

function toggleUserStatus(userId, status) {
    var action = status ? '启用' : '禁用';
    if (!confirm(`确定要${action}这个用户吗？`)) {
        return;
    }
    
    $.ajax({
        url: `/admin/users/${userId}/toggle`,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({status: status}),
        success: function(response) {
            alert(response.message);
            location.reload();
        },
        error: function(xhr) {
            var error = xhr.responseJSON ? xhr.responseJSON.error : '操作失败';
            alert(error);
        }
    });
}

function deleteUser(userId) {
    if (!confirm('确定要删除这个用户吗？此操作不可恢复！')) {
        return;
    }
    
    $.ajax({
        url: `/admin/users/${userId}`,
        method: 'DELETE',
        success: function(response) {
            alert(response.message);
            location.reload();
        },
        error: function(xhr) {
            var error = xhr.responseJSON ? xhr.responseJSON.error : '删除失败';
            alert(error);
        }
    });
}
</script>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    text-transform: uppercase;
}
</style>
{% endblock %}
