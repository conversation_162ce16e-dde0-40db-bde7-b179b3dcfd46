// 黔南民族中学学生情绪分析系统 - 主要JavaScript文件

$(document).ready(function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 自动隐藏Flash消息
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // 情绪分析表单提交
    $('#emotion-form').on('submit', function(e) {
        var submitBtn = $(this).find('button[type="submit"]');
        var originalText = submitBtn.html();
        
        // 显示加载状态
        submitBtn.html('<span class="loading-spinner"></span> 分析中...');
        submitBtn.prop('disabled', true);
        
        // 如果是AJAX提交，取消默认行为
        if ($(this).hasClass('ajax-form')) {
            e.preventDefault();
            analyzeEmotion();
        }
    });

    // AJAX情绪分析
    function analyzeEmotion() {
        var text = $('#text-input').val();
        var sourceType = $('#source-type').val() || 'manual';
        
        $.ajax({
            url: '/api/emotion/analyze',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                text: text,
                source_type: sourceType,
                save: true
            }),
            success: function(response) {
                displayEmotionResult(response);
                resetForm();
            },
            error: function(xhr) {
                var error = xhr.responseJSON ? xhr.responseJSON.error : '分析失败';
                showAlert('error', error);
                resetForm();
            }
        });
    }

    // 显示情绪分析结果
    function displayEmotionResult(result) {
        var resultHtml = `
            <div class="emotion-result">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h4>分析结果</h4>
                        <div class="emotion-score">${result.predicted_emotion}</div>
                        <p class="mb-2">置信度: ${(result.confidence * 100).toFixed(1)}%</p>
                        <div class="confidence-bar">
                            <div class="confidence-fill" style="width: ${result.confidence * 100}%"></div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="emotion-details">
                            <h6>详细分数</h6>
                            ${Object.entries(result.emotion_scores).map(([emotion, score]) => `
                                <div class="emotion-item">
                                    <span>${emotion}</span>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2">${(score * 100).toFixed(1)}%</span>
                                        <div class="emotion-bar">
                                            <div class="emotion-bar-fill bg-primary" style="width: ${score * 100}%"></div>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        $('#analysis-result').html(resultHtml).show();
        
        // 显示成功消息
        var emotionType = result.is_negative ? '负面' : (result.is_positive ? '正面' : '中性');
        showAlert('success', `情绪分析完成！检测到${emotionType}情绪：${result.predicted_emotion}`);
    }

    // 重置表单
    function resetForm() {
        var submitBtn = $('#emotion-form button[type="submit"]');
        submitBtn.html('<i class="fas fa-brain"></i> 开始分析');
        submitBtn.prop('disabled', false);
    }

    // 显示警告消息
    function showAlert(type, message) {
        var alertClass = type === 'error' ? 'danger' : type;
        var alertHtml = `
            <div class="alert alert-${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        $('.container').prepend(alertHtml);
        
        // 自动隐藏
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    }

    // 批量分析功能
    $('#batch-analyze-btn').on('click', function() {
        var texts = $('#batch-text').val().split('\n').filter(text => text.trim());
        
        if (texts.length === 0) {
            showAlert('error', '请输入要分析的文本内容');
            return;
        }
        
        var btn = $(this);
        var originalText = btn.html();
        btn.html('<span class="loading-spinner"></span> 批量分析中...');
        btn.prop('disabled', true);
        
        $.ajax({
            url: '/emotion/api/batch_analyze',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                texts: texts,
                save: true,
                source_type: 'batch'
            }),
            success: function(response) {
                displayBatchResults(response);
                btn.html(originalText);
                btn.prop('disabled', false);
            },
            error: function(xhr) {
                var error = xhr.responseJSON ? xhr.responseJSON.error : '批量分析失败';
                showAlert('error', error);
                btn.html(originalText);
                btn.prop('disabled', false);
            }
        });
    });

    // 显示批量分析结果
    function displayBatchResults(response) {
        var resultsHtml = `
            <div class="batch-results mt-4">
                <h5>批量分析结果 (成功: ${response.success_count}, 失败: ${response.error_count})</h5>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>文本内容</th>
                                <th>预测情绪</th>
                                <th>置信度</th>
                                <th>情绪类型</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${response.results.map((result, index) => `
                                <tr>
                                    <td>${result.text.substring(0, 50)}${result.text.length > 50 ? '...' : ''}</td>
                                    <td>
                                        ${result.error ? 
                                            '<span class="text-danger">分析失败</span>' : 
                                            `<span class="emotion-label emotion-${result.predicted_emotion}">${result.predicted_emotion}</span>`
                                        }
                                    </td>
                                    <td>
                                        ${result.error ? '-' : (result.confidence * 100).toFixed(1) + '%'}
                                    </td>
                                    <td>
                                        ${result.error ? '-' : 
                                            (result.is_negative ? '<span class="text-danger">负面</span>' : 
                                             result.is_positive ? '<span class="text-success">正面</span>' : 
                                             '<span class="text-secondary">中性</span>')
                                        }
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
        
        $('#batch-results').html(resultsHtml);
        showAlert('success', `批量分析完成！成功分析 ${response.success_count} 条文本`);
    }

    // 处理预警
    $('.handle-alert-btn').on('click', function() {
        var alertId = $(this).data('alert-id');
        var action = $(this).data('action');
        var note = prompt('请输入处理备注（可选）：');
        
        if (note === null) return; // 用户取消
        
        $.ajax({
            url: `/admin/alerts/${alertId}/handle`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                action: action,
                note: note
            }),
            success: function(response) {
                showAlert('success', response.message);
                location.reload(); // 刷新页面
            },
            error: function(xhr) {
                var error = xhr.responseJSON ? xhr.responseJSON.error : '处理失败';
                showAlert('error', error);
            }
        });
    });

    // 切换用户状态
    $('.toggle-user-status').on('click', function() {
        var userId = $(this).data('user-id');
        var userName = $(this).data('user-name');
        var currentStatus = $(this).data('current-status');
        var action = currentStatus ? '禁用' : '启用';
        
        if (!confirm(`确定要${action}用户 ${userName} 吗？`)) {
            return;
        }
        
        $.ajax({
            url: `/admin/users/${userId}/toggle_status`,
            method: 'POST',
            success: function(response) {
                showAlert('success', response.message);
                location.reload(); // 刷新页面
            },
            error: function(xhr) {
                var error = xhr.responseJSON ? xhr.responseJSON.error : '操作失败';
                showAlert('error', error);
            }
        });
    });

    // 图表颜色配置
    window.chartColors = {
        emotions: {
            '高兴': '#28a745',
            '喜好': '#20c997',
            '惊讶': '#6f42c1',
            '恐惧': '#fd7e14',
            '愤怒': '#dc3545',
            '厌恶': '#6c757d',
            '悲伤': '#007bff'
        }
    };
});
