#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员视图
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from functools import wraps
from app import db
from app.models.user import User
from app.models.emotion import EmotionRecord, EmotionAlert
from datetime import datetime, timedelta
import logging

bp = Blueprint('admin', __name__)

def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin():
            flash('需要管理员权限', 'error')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function

@bp.route('/')
@login_required
@admin_required
def index():
    """管理员首页"""
    try:
        # 统计数据
        total_users = User.query.count()
        total_students = User.query.filter_by(user_type='student').count()
        total_teachers = User.query.filter_by(user_type='teacher').count()
        
        # 最近7天的情绪记录统计
        week_ago = datetime.utcnow() - timedelta(days=7)
        recent_records = EmotionRecord.query.filter(
            EmotionRecord.created_at >= week_ago
        ).count()
        
        # 待处理预警数量
        pending_alerts = EmotionAlert.query.filter_by(status='pending').count()
        
        # 最近的预警
        recent_alerts = EmotionAlert.query.order_by(
            EmotionAlert.created_at.desc()
        ).limit(10).all()
        
        return render_template('admin/index.html',
                             total_users=total_users,
                             total_students=total_students,
                             total_teachers=total_teachers,
                             recent_records=recent_records,
                             pending_alerts=pending_alerts,
                             recent_alerts=recent_alerts)
        
    except Exception as e:
        logging.error(f"管理员首页加载失败: {str(e)}")
        flash('数据加载失败，请稍后重试', 'error')
        return render_template('admin/index.html')

@bp.route('/users')
@login_required
@admin_required
def users():
    """用户管理"""
    try:
        page = request.args.get('page', 1, type=int)
        user_type = request.args.get('type', '')
        search = request.args.get('search', '')
        
        query = User.query
        
        if user_type:
            query = query.filter_by(user_type=user_type)
        
        if search:
            query = query.filter(
                (User.username.contains(search)) |
                (User.real_name.contains(search)) |
                (User.email.contains(search))
            )
        
        users = query.order_by(User.created_at.desc()).paginate(
            page=page, per_page=20, error_out=False
        )
        
        return render_template('admin/users.html', users=users,
                             user_type=user_type, search=search)
        
    except Exception as e:
        logging.error(f"用户管理页面加载失败: {str(e)}")
        flash('用户数据加载失败，请稍后重试', 'error')
        return render_template('admin/users.html', users=None)

@bp.route('/users/<int:user_id>/toggle_status', methods=['POST'])
@login_required
@admin_required
def toggle_user_status(user_id):
    """切换用户状态"""
    try:
        user = User.query.get_or_404(user_id)
        
        if user.id == current_user.id:
            return jsonify({'error': '不能禁用自己的账户'}), 400
        
        user.is_active = not user.is_active
        db.session.commit()
        
        status = '启用' if user.is_active else '禁用'
        return jsonify({
            'success': True,
            'message': f'用户 {user.real_name} 已{status}',
            'is_active': user.is_active
        })
        
    except Exception as e:
        logging.error(f"切换用户状态失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@bp.route('/alerts')
@login_required
@admin_required
def alerts():
    """预警管理"""
    try:
        page = request.args.get('page', 1, type=int)
        status = request.args.get('status', '')
        alert_level = request.args.get('level', '')
        
        query = EmotionAlert.query
        
        if status:
            query = query.filter_by(status=status)
        
        if alert_level:
            query = query.filter_by(alert_level=alert_level)
        
        alerts = query.order_by(EmotionAlert.created_at.desc()).paginate(
            page=page, per_page=20, error_out=False
        )
        
        return render_template('admin/alerts.html', alerts=alerts,
                             status=status, alert_level=alert_level)
        
    except Exception as e:
        logging.error(f"预警管理页面加载失败: {str(e)}")
        flash('预警数据加载失败，请稍后重试', 'error')
        return render_template('admin/alerts.html', alerts=None)

@bp.route('/alerts/<int:alert_id>/handle', methods=['POST'])
@login_required
@admin_required
def handle_alert(alert_id):
    """处理预警"""
    try:
        alert = EmotionAlert.query.get_or_404(alert_id)
        data = request.get_json()
        
        action = data.get('action')  # 'processing' 或 'resolved'
        note = data.get('note', '')
        
        if action == 'processing':
            alert.status = 'processing'
            alert.handler_id = current_user.id
            alert.handler_note = note
        elif action == 'resolved':
            alert.status = 'resolved'
            alert.handler_id = current_user.id
            alert.handler_note = note
            alert.resolved_at = datetime.utcnow()
        else:
            return jsonify({'error': '无效的操作'}), 400
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'预警已标记为{alert.status}',
            'status': alert.status
        })
        
    except Exception as e:
        logging.error(f"处理预警失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@bp.route('/reports')
@login_required
@admin_required
def reports():
    """报告页面"""
    try:
        # 获取时间范围
        days = request.args.get('days', 30, type=int)
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # 情绪记录统计
        emotion_records = EmotionRecord.query.filter(
            EmotionRecord.created_at >= start_date
        ).all()
        
        # 按情绪类型统计
        emotion_stats = {}
        daily_stats = {}
        
        for record in emotion_records:
            emotion = record.predicted_emotion
            date_key = record.created_at.strftime('%Y-%m-%d')
            
            emotion_stats[emotion] = emotion_stats.get(emotion, 0) + 1
            
            if date_key not in daily_stats:
                daily_stats[date_key] = 0
            daily_stats[date_key] += 1
        
        # 预警统计
        alert_stats = EmotionAlert.query.filter(
            EmotionAlert.created_at >= start_date
        ).all()
        
        alert_by_level = {}
        alert_by_status = {}
        
        for alert in alert_stats:
            level = alert.alert_level
            status = alert.status
            
            alert_by_level[level] = alert_by_level.get(level, 0) + 1
            alert_by_status[status] = alert_by_status.get(status, 0) + 1
        
        return render_template('admin/reports.html',
                             emotion_stats=emotion_stats,
                             daily_stats=daily_stats,
                             alert_by_level=alert_by_level,
                             alert_by_status=alert_by_status,
                             total_records=len(emotion_records),
                             total_alerts=len(alert_stats),
                             days=days)
        
    except Exception as e:
        logging.error(f"报告页面加载失败: {str(e)}")
        flash('报告数据加载失败，请稍后重试', 'error')
        return render_template('admin/reports.html')

@bp.route('/users/<int:user_id>')
@login_required
@admin_required
def api_get_user(user_id):
    """获取用户详情API"""
    try:
        user = User.query.get_or_404(user_id)

        result = user.to_dict()
        result['emotion_count'] = EmotionRecord.query.filter_by(user_id=user_id).count()

        return jsonify(result)

    except Exception as e:
        logging.error(f"获取用户详情失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@bp.route('/users', methods=['POST'])
@login_required
@admin_required
def api_create_user():
    """创建用户API"""
    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['username', 'real_name', 'role', 'password']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'缺少必填字段: {field}'}), 400

        # 检查用户名是否已存在
        if User.query.filter_by(username=data['username']).first():
            return jsonify({'error': '用户名已存在'}), 400

        # 创建用户
        user = User(
            username=data['username'],
            real_name=data['real_name'],
            email=data.get('email'),
            role=data['role'],
            student_id=data.get('student_id') if data['role'] == 'student' else None,
            class_name=data.get('class_name') if data['role'] == 'student' else None
        )
        user.set_password(data['password'])

        db.session.add(user)
        db.session.commit()

        return jsonify({'message': '用户创建成功', 'user_id': user.id})

    except Exception as e:
        logging.error(f"创建用户失败: {str(e)}")
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@bp.route('/users/<int:user_id>/toggle', methods=['POST'])
@login_required
@admin_required
def api_toggle_user_status(user_id):
    """切换用户状态API"""
    try:
        user = User.query.get_or_404(user_id)
        data = request.get_json()

        user.is_active = data.get('status', True)
        db.session.commit()

        action = '启用' if user.is_active else '禁用'
        return jsonify({'message': f'用户{action}成功'})

    except Exception as e:
        logging.error(f"切换用户状态失败: {str(e)}")
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@bp.route('/users/<int:user_id>', methods=['DELETE'])
@login_required
@admin_required
def api_delete_user(user_id):
    """删除用户API"""
    try:
        user = User.query.get_or_404(user_id)

        # 不能删除自己
        if user.id == current_user.id:
            return jsonify({'error': '不能删除自己的账户'}), 400

        # 删除相关记录
        EmotionRecord.query.filter_by(user_id=user_id).delete()
        EmotionAlert.query.filter_by(user_id=user_id).delete()

        # 删除用户
        db.session.delete(user)
        db.session.commit()

        return jsonify({'message': '用户删除成功'})

    except Exception as e:
        logging.error(f"删除用户失败: {str(e)}")
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
